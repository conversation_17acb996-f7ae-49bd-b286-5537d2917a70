{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM"], "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "types": ["node"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*", "*.ts", "*.js"], "exclude": ["node_modules", "dist", "frontend"]}