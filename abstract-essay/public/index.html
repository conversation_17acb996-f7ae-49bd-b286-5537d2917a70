<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Akademik Makale Özet Sistemi</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #3d6a80 0%, #2c5aa0 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .input-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .input-group {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .input-group label {
            font-weight: bold;
            color: #333;
            min-width: 100px;
        }
        
        .input-group input {
            flex: 1;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            min-width: 300px;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #3d6a80;
            box-shadow: 0 0 0 3px rgba(61, 106, 128, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #3d6a80 0%, #2c5aa0 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3d6a80;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .result {
            display: none;
            margin-top: 30px;
        }
        
        .result-header {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            border-left: 5px solid #3d6a80;
        }
        
        .result-content {
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 10px 10px;
            padding: 20px;
        }
        
        .svg-container {
            text-align: center;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            margin: 20px 0;
        }
        
        .examples {
            margin-top: 20px;
        }
        
        .examples h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .example-doi {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .example-doi:hover {
            background: #bbdefb;
        }
        
        .download-btn {
            background: #28a745;
            margin-left: 10px;
        }
        
        .download-btn:hover {
            background: #218838;
        }

        .quality-full {
            color: #198754;
            font-weight: bold;
        }

        .quality-partial {
            color: #fd7e14;
            font-weight: bold;
        }

        .quality-limited {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 Akademik Makale Özet Sistemi</h1>
            <p>Tüm akademik alanlardan makalelerin yapay zeka destekli özet ve görselleştirme platformu</p>
        </div>
        
        <div class="content">
            <div class="input-section">
                <div class="input-group">
                    <label for="doiInput">DOI Numarası:</label>
                    <input type="text" id="doiInput" placeholder="Örn: 10.1016/j.kint.2021.02.040" />
                    <button class="btn" onclick="processDOI()">Analiz Et</button>
                </div>
                
                <div class="examples">
                    <h3>Örnek DOI Numaraları (Farklı Alanlardan):</h3>
                    <div class="example-doi" onclick="setDOI('10.1016/j.kint.2021.02.040')">
                        10.1016/j.kint.2021.02.040 - Tıp: Paclitaxel-coated balloons for arteriovenous fistulas
                    </div>
                    <div class="example-doi" onclick="setDOI('10.1038/s41586-021-03819-2')">
                        10.1038/s41586-021-03819-2 - Fizik: Quantum computing breakthrough
                    </div>
                    <div class="example-doi" onclick="setDOI('10.1145/3447548.3467401')">
                        10.1145/3447548.3467401 - Bilgisayar: Machine learning algorithms
                    </div>
                    <div class="example-doi" onclick="setDOI('10.1007/s44163-022-00022-8')">
                        10.1007/s44163-022-00022-8 - Sosyal Bilimler: Technology-based problem solving
                    </div>
                </div>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Makale analiz ediliyor, lütfen bekleyiniz...</p>
            </div>
            
            <div class="result" id="result">
                <div class="result-header">
                    <h2>📊 Makale Özeti</h2>
                </div>
                <div class="result-content">
                    <div id="summaryText"></div>
                    <div class="svg-container">
                        <div id="svgContent"></div>
                        <button class="btn download-btn" onclick="downloadSVG()">SVG İndir</button>
                    </div>
                </div>
            </div>
            
            <div class="error" id="error" style="display: none;"></div>
        </div>
    </div>

    <script>
        let currentSVG = '';
        
        function setDOI(doi) {
            document.getElementById('doiInput').value = doi;
        }
        
        async function processDOI() {
            const doiInput = document.getElementById('doiInput').value.trim();
            if (!doiInput) {
                showError('Lütfen bir DOI numarası giriniz.');
                return;
            }

            showLoading(true);
            hideError();
            hideResult();

            try {
                console.log('DOI işleniyor:', doiInput);

                // Önce API Proxy'yi dene
                try {
                    console.log('API Proxy çağrılıyor...');
                    const response = await fetch('http://localhost:3001/api/analyze-doi', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({
                            doi: doiInput
                        })
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`Proxy API Hatası: ${response.status} - ${errorText}`);
                    }

                    const result = await response.json();
                    console.log('Proxy API Sonucu:', result);

                    if (result.error) {
                        throw new Error(result.error);
                    }

                    // Sonucu göster
                    showResult(result);
                    return; // Başarılı olursa fallback'e gitme

                } catch (proxyError) {
                    console.warn('Proxy API başarısız, Mastra API deneniyor...', proxyError.message);

                    // Mastra API'yi dene
                    try {
                        const response = await fetch('http://localhost:4111/api/workflows/doi-summary-workflow/execute', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                doi: doiInput
                            })
                        });

                        if (!response.ok) {
                            const errorText = await response.text();
                            throw new Error(`Mastra API Hatası: ${response.status} - ${errorText}`);
                        }

                        const result = await response.json();
                        console.log('Mastra API Sonucu:', result);

                        if (result.error) {
                            throw new Error(result.error);
                        }

                        // Sonucu göster
                        showResult(result);
                        return;

                    } catch (mastraError) {
                        console.warn('Mastra API de başarısız:', mastraError.message);
                        throw new Error(`Her iki API de başarısız: ${proxyError.message} | ${mastraError.message}`);
                    }
                }

            } catch (error) {
                console.error('İşlem hatası:', error);
                showError('Makale işlenirken bir hata oluştu: ' + error.message);

                // Fallback: Mock veri göster
                console.log('Fallback mock veri kullanılıyor...');
                const mockResult = {
                    svg: generateMockSVG(),
                    summary: {
                        title: "Örnek Makale Başlığı",
                        authors: "Yazar1, Yazar2, et al.",
                        journal: "Örnek Dergi",
                        year: "2024",
                        field: "Genel",
                        abstract: "Bu bir örnek makale özetidir. API bağlantısı kurulamadığı için mock veri gösteriliyor.",
                        findings: "• Örnek bulgu 1\n• Örnek bulgu 2\n• Örnek bulgu 3",
                        conclusion: "Örnek sonuç metni.",
                        methodology: "Örnek metodoloji açıklaması.",
                        methods: "Örnek yöntemler.",
                        dataQuality: "LIMITED",
                        keywords: "örnek, anahtar, kelimeler"
                    }
                };

                showResult(mockResult);

            } finally {
                showLoading(false);
            }
        }
        
        function simulateProcessing() {
            return new Promise(resolve => setTimeout(resolve, 3000));
        }
        
        function generateMockSVG() {
            return `<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600">
                <rect width="800" height="600" fill="#f8f9fa"/>
                <rect width="800" height="60" fill="#3d6a80"/>
                <text x="20" y="35" font-size="20" font-weight="bold" fill="white">Makale Özet Görselleştirmesi</text>
                <rect x="20" y="80" width="360" height="200" fill="#e9ecef" rx="5"/>
                <text x="30" y="105" font-size="16" font-weight="bold" fill="#333">Metodoloji</text>
                <text x="30" y="130" font-size="12" fill="#333">Çok merkezli randomize kontrollü çalışma</text>
                <rect x="400" y="80" width="380" height="200" fill="#d1e7dd" rx="5"/>
                <text x="410" y="105" font-size="16" font-weight="bold" fill="#0f5132">Ana Bulgular</text>
                <text x="410" y="130" font-size="12" fill="#0f5132">Paklitaksel kaplı balonlar fayda sağlamadı</text>
                <rect x="20" y="300" width="760" height="150" fill="#f8d7da" rx="5"/>
                <text x="30" y="325" font-size="16" font-weight="bold" fill="#842029">Sonuç</text>
                <text x="30" y="350" font-size="12" fill="#842029">Geleneksel tedaviye kıyasla üstünlük göstermedi</text>
            </svg>`;
        }
        
        function showResult(result) {
            const summaryText = document.getElementById('summaryText');
            const summary = result.summary || result; // API'den gelen format için uyumluluk

            summaryText.innerHTML = `
                <h3>${summary.title}</h3>
                <p><strong>Yazarlar:</strong> ${summary.authors}</p>
                <p><strong>Dergi:</strong> ${summary.journal} (${summary.year})</p>
                <p><strong>Alan:</strong> ${summary.field || 'Belirtilmemiş'}</p>
                <p><strong>Veri Kalitesi:</strong> <span class="quality-${(summary.dataQuality || 'LIMITED').toLowerCase()}">${summary.dataQuality || 'LIMITED'}</span></p>
                <p><strong>Özet:</strong> ${summary.abstract}</p>
                <p><strong>Ana Bulgular:</strong> ${(summary.findings || '').replace(/\n/g, '<br>')}</p>
                <p><strong>Sonuç:</strong> ${summary.conclusion}</p>
                <p><strong>Metodoloji:</strong> ${summary.methodology}</p>
                <p><strong>Yöntemler:</strong> ${summary.methods || summary.statistics || 'Belirtilmemiş'}</p>
                ${summary.keywords ? `<p><strong>Anahtar Kelimeler:</strong> ${summary.keywords}</p>` : ''}
                ${summary.citationCount ? `<p><strong>Atıf Sayısı:</strong> ${summary.citationCount}</p>` : ''}
            `;

            const svgContent = document.getElementById('svgContent');
            svgContent.innerHTML = result.svg;
            currentSVG = result.svg;

            document.getElementById('result').style.display = 'block';
        }
        
        function downloadSVG() {
            if (!currentSVG) return;
            
            const blob = new Blob([currentSVG], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'makale-ozeti.svg';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
        
        function hideResult() {
            document.getElementById('result').style.display = 'none';
        }
        
        // Enter tuşu ile form gönderimi
        document.getElementById('doiInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                processDOI();
            }
        });
    </script>
</body>
</html>
