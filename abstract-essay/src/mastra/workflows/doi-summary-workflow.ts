import { createWorkflow, createStep } from '@mastra/core/workflows';
import { z } from 'zod';
import { articleSummaryAgent, createAcademicSummarySVG } from '../agents/article-summary-agent';

const extractArticleData = createStep({
  id: 'extract-article-data',
  description: 'DOI ile makale verilerini çek ve analiz et',
  inputSchema: z.object({
    doi: z.string(),
  }),
  outputSchema: z.object({
    title: z.string(),
    authors: z.string(),
    journal: z.string(),
    year: z.string(),
    field: z.string(),
    abstract: z.string(),
    findings: z.string(),
    conclusion: z.string(),
    methodology: z.string(),
    methods: z.string(),
    dataQuality: z.string(),
    keywords: z.string(),
    citationCount: z.number().optional(),
    pmid: z.string().optional(),
  }),
  execute: async ({ inputData, mastra }) => {
    if (!inputData) {
      throw new Error('Input data not found');
    }

    const agent = mastra?.getAgent('articleSummaryAgent');
    if (!agent) {
      throw new Error('Article summary agent not found');
    }

    const prompt = `DOI: ${inputData.doi}

Bu DOI numarasına sahip akademik makaleyi analiz et ve aşağıdaki bilgileri çıkar:

1. Makale başlığı, yazarlar, dergi adı ve yayın yılı
2. Makale alanını belirle (Tıp, Mühendislik, Fizik, Kimya, Bilgisayar, Sosyal Bilimler, vb.)
3. Makalenin özetini al (varsa)
4. Ana bulguları madde madde özetle (en fazla 6 madde)
5. Sonuç kısmını özetle
6. Metodoloji kısmını özetle
7. Kullanılan yöntemleri belirle (alan bazlı)
8. Veri kalitesini değerlendir

Lütfen sonuçları JSON formatında döndür:
{
  "title": "Makalenin başlığı",
  "authors": "Yazar1, Yazar2, ...",
  "journal": "Dergi adı",
  "year": "Yayın yılı",
  "field": "Makale alanı",
  "abstract": "Makalenin özeti veya 'Özet mevcut değil'",
  "findings": "Ana bulgular (madde işaretleri ile)",
  "conclusion": "Sonuç özeti",
  "methodology": "Metodoloji özeti",
  "methods": "Kullanılan yöntemler",
  "dataQuality": "FULL/PARTIAL/LIMITED",
  "keywords": "Anahtar kelimeler",
  "citationCount": 0,
  "pmid": ""
}`;

    const response = await agent.stream([
      {
        role: 'user',
        content: prompt,
      },
    ]);

    let responseText = '';
    for await (const chunk of response.textStream) {
      responseText += chunk;
    }

    // JSON'u parse etmeye çalış
    try {
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsedData = JSON.parse(jsonMatch[0]);
        return parsedData;
      }
    } catch (error) {
      console.error('JSON parse error:', error);
    }

    // Fallback: Basit metin analizi
    return {
      title: 'Başlık bulunamadı',
      authors: 'Yazar bilgisi bulunamadı',
      journal: 'Dergi bilgisi bulunamadı',
      year: 'Yıl bilgisi bulunamadı',
      field: 'Genel',
      abstract: responseText.slice(0, 500),
      findings: 'Bulgular analiz edilemedi',
      conclusion: 'Sonuç analiz edilemedi',
      methodology: 'Metodoloji analiz edilemedi',
      methods: 'Yöntemler analiz edilemedi',
      dataQuality: 'LIMITED',
      keywords: '',
      citationCount: 0,
      pmid: '',
    };
  },
});

const generateSVG = createStep({
  id: 'generate-svg',
  description: 'Makale verilerinden SVG infografik oluştur',
  inputSchema: z.object({
    title: z.string(),
    authors: z.string(),
    journal: z.string(),
    year: z.string(),
    field: z.string(),
    abstract: z.string(),
    findings: z.string(),
    conclusion: z.string(),
    methodology: z.string(),
    methods: z.string(),
    dataQuality: z.string(),
    keywords: z.string(),
    citationCount: z.number().optional(),
    pmid: z.string().optional(),
  }),
  outputSchema: z.object({
    svg: z.string(),
    summary: z.object({
      title: z.string(),
      authors: z.string(),
      journal: z.string(),
      year: z.string(),
      field: z.string(),
      abstract: z.string(),
      findings: z.string(),
      conclusion: z.string(),
      methodology: z.string(),
      methods: z.string(),
      dataQuality: z.string(),
      keywords: z.string(),
      citationCount: z.number().optional(),
      pmid: z.string().optional(),
    }),
  }),
  execute: async ({ inputData }) => {
    if (!inputData) {
      throw new Error('Input data not found');
    }

    const svg = createAcademicSummarySVG({
      title: inputData.title,
      authors: inputData.authors,
      journal: inputData.journal,
      year: inputData.year,
      field: inputData.field,
      abstract: inputData.abstract,
      findings: inputData.findings,
      conclusion: inputData.conclusion,
      methodology: inputData.methodology,
      methods: inputData.methods,
      dataQuality: inputData.dataQuality,
      keywords: inputData.keywords,
      citationCount: inputData.citationCount || 0,
      pmid: inputData.pmid || '',
    });

    return {
      svg,
      summary: {
        title: inputData.title,
        authors: inputData.authors,
        journal: inputData.journal,
        year: inputData.year,
        field: inputData.field,
        abstract: inputData.abstract,
        findings: inputData.findings,
        conclusion: inputData.conclusion,
        methodology: inputData.methodology,
        methods: inputData.methods,
        dataQuality: inputData.dataQuality,
        keywords: inputData.keywords,
        citationCount: inputData.citationCount || 0,
        pmid: inputData.pmid || '',
      },
    };
  },
});

export const doiSummaryWorkflow = createWorkflow({
  id: 'doi-summary-workflow',
  inputSchema: z.object({
    doi: z.string().describe('DOI numarası (örn: 10.1016/j.kint.2021.02.040)'),
  }),
  outputSchema: z.object({
    svg: z.string(),
    summary: z.object({
      title: z.string(),
      authors: z.string(),
      journal: z.string(),
      year: z.string(),
      field: z.string(),
      abstract: z.string(),
      findings: z.string(),
      conclusion: z.string(),
      methodology: z.string(),
      methods: z.string(),
      dataQuality: z.string(),
      keywords: z.string(),
      citationCount: z.number().optional(),
      pmid: z.string().optional(),
    }),
  }),
})
  .then(extractArticleData)
  .then(generateSVG);

doiSummaryWorkflow.commit();
