
import { <PERSON><PERSON> } from '@mastra/core/mastra';
import { Pi<PERSON>Logger } from '@mastra/loggers';
import { LibSQLStore } from '@mastra/libsql';
import { weatherWorkflow } from './workflows/weather-workflow';
import { doiSummaryWorkflow } from './workflows/doi-summary-workflow';
import { weatherAgent } from './agents/weather-agent';
import { articleSummaryAgent } from './agents/article-summary-agent';

export const mastra = new Mastra({
  workflows: { weatherWorkflow, doiSummaryWorkflow },
  agents: { weatherAgent, articleSummaryAgent },
  storage: new LibSQLStore({
    // stores telemetry, evals, ... into memory storage, if it needs to persist, change to file:../mastra.db
    url: ":memory:",
  }),
  logger: new PinoLogger({
    name: '<PERSON><PERSON>',
    level: 'info',
  }),
});
