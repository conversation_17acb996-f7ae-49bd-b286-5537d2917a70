# frozen_string_literal: true

require 'sinatra'
require 'sinatra/cors'
require 'json'
require 'httparty'
require 'open3'
require 'logger'
require 'dotenv/load'

# Configuration
configure do
  # CORS ayarları
  set :allow_origin, '*'
  set :allow_methods, 'GET,HEAD,POST,OPTIONS'
  set :allow_headers, 'content-type,if-modified-since,authorization'
  set :expose_headers, 'location,link'

  # Server ayarları
  set :port, ENV.fetch('PORT', 4567)
  set :bind, ENV.fetch('BIND', '0.0.0.0')
  set :environment, ENV.fetch('RACK_ENV', 'development')

  # JSON pretty print in development
  set :json_encoder, :to_json
end

# Logger ayarları
LOGGER = Logger.new($stdout)
LOGGER.level = ENV.fetch('LOG_LEVEL', 'INFO').upcase.to_sym
LOGGER.formatter = proc do |severity, datetime, progname, msg|
  "[#{datetime.strftime('%Y-%m-%d %H:%M:%S')}] #{severity}: #{msg}\n"
end

# Services
module Services
  # Unpaywall API ile PDF link alma
  class UnpaywallService
    include HTTParty
    base_uri 'https://api.unpaywall.org'

    TIMEOUT = 10
    DEFAULT_EMAIL = ENV.fetch('UNPAYWALL_EMAIL', '<EMAIL>')

    def self.get_pdf_link(doi, email = DEFAULT_EMAIL)
      LOGGER.info("Fetching PDF link for DOI: #{doi}")

      response = get("/v2/#{doi}?email=#{email}", timeout: TIMEOUT)

      if response.success?
        data = response.parsed_response

        # En iyi PDF linkini bul
        if data['is_oa'] && data['best_oa_location']
          result = {
            is_open_access: true,
            pdf_url: data['best_oa_location']['url_for_pdf'],
            host_type: data['best_oa_location']['host_type'],
            license: data['best_oa_location']['license'],
            journal_name: data['journal_name'],
            publisher: data['publisher']
          }
          LOGGER.info("Found open access PDF for DOI: #{doi}")
          result
        else
          LOGGER.info("No open access PDF found for DOI: #{doi}")
          { is_open_access: false, pdf_url: nil }
        end
      else
        error_msg = "Unpaywall API error: #{response.code}"
        LOGGER.error(error_msg)
        { is_open_access: false, pdf_url: nil, error: error_msg }
      end
    rescue Net::TimeoutError => e
      error_msg = "Unpaywall API timeout: #{e.message}"
      LOGGER.error(error_msg)
      { is_open_access: false, pdf_url: nil, error: error_msg }
    rescue => e
      error_msg = "Unpaywall API error: #{e.message}"
      LOGGER.error(error_msg)
      { is_open_access: false, pdf_url: nil, error: error_msg }
    end
  end

  # Mastra agent'ı çalıştırma servisi
  class MastraService
    CLI_TIMEOUT = 30

    def self.analyze_doi(doi)
      LOGGER.info("Starting Mastra analysis for DOI: #{doi}")

      # CLI agent'ı çalıştır
      command = build_command(doi)

      stdout, stderr, status = Open3.capture3(command, timeout: CLI_TIMEOUT)

      if status.success?
        parse_analysis_result(stdout, doi)
      else
        LOGGER.error("CLI agent failed for DOI #{doi}: #{stderr}")
        LOGGER.error("Raw output: #{stdout}")
        create_fallback_analysis(doi, "CLI execution failed: #{stderr}")
      end
    rescue Timeout::Error => e
      error_msg = "CLI agent timeout for DOI #{doi}: #{e.message}"
      LOGGER.error(error_msg)
      create_fallback_analysis(doi, error_msg)
    rescue => e
      error_msg = "Unexpected error analyzing DOI #{doi}: #{e.message}"
      LOGGER.error(error_msg)
      create_fallback_analysis(doi, error_msg)
    end

    private

    def self.build_command(doi)
      escaped_doi = doi.gsub("'", "\\'")
      "cd .. && timeout #{CLI_TIMEOUT} node cli-agent.js '#{escaped_doi}'"
    end

    def self.parse_analysis_result(stdout, doi)
      JSON.parse(stdout)
    rescue JSON::ParserError => e
      LOGGER.error("JSON parse error for DOI #{doi}: #{e.message}")
      LOGGER.error("Raw output: #{stdout}")
      create_fallback_analysis(doi, "Invalid JSON response from CLI agent")
    end

    def self.create_fallback_analysis(doi, error_reason = 'Unknown error')
      LOGGER.warn("Creating fallback analysis for DOI: #{doi}")

      {
        'title' => 'Makale analizi yapılamadı',
        'authors' => 'Bilinmiyor',
        'journal' => 'Bilinmiyor',
        'year' => 'Bilinmiyor',
        'field' => 'Genel',
        'abstract' => 'Özet alınamadı',
        'findings' => '• Analiz yapılamadı\n• Lütfen daha sonra tekrar deneyin',
        'conclusion' => 'Sonuç analiz edilemedi',
        'methodology' => 'Metodoloji bilgisi alınamadı',
        'methods' => 'Yöntem bilgisi alınamadı',
        'dataQuality' => 'LIMITED',
        'limitations' => 'Sistem hatası nedeniyle analiz tamamlanamadı',
        'keywords' => '',
        'citationCount' => 0,
        'pmid' => '',
        'error' => error_reason,
        'fallback' => true
      }
    end
  end
end

# Helpers
helpers do
  def validate_doi(doi)
    return false if doi.nil? || doi.strip.empty?

    # Basic DOI format validation
    clean_doi = clean_doi_format(doi)
    clean_doi.match?(/^10\.\d{4,}\/\S+$/)
  end

  def clean_doi_format(doi)
    doi.gsub(/^https?:\/\/(dx\.)?doi\.org\//, '').strip
  end

  def json_response(data, status_code = 200)
    content_type :json
    status status_code

    if settings.development?
      JSON.pretty_generate(data)
    else
      data.to_json
    end
  end

  def error_response(message, status_code = 400, details = nil)
    error_data = { error: message }
    error_data[:details] = details if details
    error_data[:timestamp] = Time.now.iso8601

    json_response(error_data, status_code)
  end
end

# Middleware
before do
  # Request logging
  LOGGER.info("#{request.request_method} #{request.path_info} from #{request.ip}")

  # Set response headers
  headers 'X-API-Version' => '1.0'
  headers 'X-Powered-By' => 'Ruby/Sinatra'
end

# Routes

# Health check endpoint
get '/health' do
  json_response({
    status: 'OK',
    timestamp: Time.now.iso8601,
    version: '1.0',
    environment: settings.environment
  })
end

# API info endpoint
get '/info' do
  json_response({
    name: 'Abstract Essay API',
    version: '1.0',
    description: 'Academic paper analysis API using DOI',
    endpoints: {
      health: '/health',
      summary: '/summary?doi=<DOI>',
      info: '/info'
    },
    example: '/summary?doi=10.1016/j.kint.2021.02.040'
  })
end

# Ana endpoint - DOI analizi
get '/summary' do
  doi = params['doi']

  # DOI validation
  unless validate_doi(doi)
    return error_response('Geçerli bir DOI parametresi gerekli. Örnek: 10.1016/j.kint.2021.02.040')
  end

  # DOI'yi temizle
  clean_doi = clean_doi_format(doi)

  LOGGER.info("Processing DOI: #{clean_doi}")

  begin
    start_time = Time.now

    # Paralel olarak Mastra analizi ve Unpaywall sorgusu yap
    analysis_thread = Thread.new { Services::MastraService.analyze_doi(clean_doi) }
    unpaywall_thread = Thread.new { Services::UnpaywallService.get_pdf_link(clean_doi) }

    # Sonuçları bekle
    analysis_result = analysis_thread.value
    unpaywall_result = unpaywall_thread.value

    processing_time = (Time.now - start_time).round(2)

    # Sonuçları birleştir
    result = {
      doi: clean_doi,
      analysis: analysis_result,
      pdf_access: unpaywall_result,
      processing_time_seconds: processing_time,
      processed_at: Time.now.iso8601,
      api_version: '1.0'
    }

    LOGGER.info("Analysis completed for DOI: #{clean_doi} in #{processing_time}s")
    json_response(result)

  rescue => e
    LOGGER.error("Error processing DOI #{clean_doi}: #{e.message}")
    LOGGER.error("Backtrace: #{e.backtrace.first(5).join('\n')}")

    error_response(
      'Makale analizi sırasında hata oluştu',
      500,
      {
        doi: clean_doi,
        error_type: e.class.name,
        message: e.message
      }
    )
  end
end

# CORS preflight
options '*' do
  response.headers['Allow'] = 'GET, POST, OPTIONS'
  response.headers['Access-Control-Allow-Headers'] = 'Authorization, Content-Type, Accept, X-User-Email, X-Auth-Token'
  response.headers['Access-Control-Allow-Origin'] = '*'
  200
end

# Error handlers

# 404 handler
not_found do
  error_response('Endpoint bulunamadı', 404)
end

# 500 handler
error do
  error_msg = env['sinatra.error'].message
  LOGGER.error("Unhandled error: #{error_msg}")
  LOGGER.error("Backtrace: #{env['sinatra.error'].backtrace.first(5).join('\n')}")

  error_response(
    'Sunucu hatası',
    500,
    {
      error_type: env['sinatra.error'].class.name,
      message: error_msg
    }
  )
end

# Graceful shutdown
at_exit do
  LOGGER.info("Shutting down Abstract Essay API...")
end

# Server başlatma bilgisi
if __FILE__ == $0
  puts "\n" + "="*60
  puts "🚀 Abstract Essay API - Ruby Backend"
  puts "="*60
  puts "📡 API Endpoints:"
  puts "   • Health Check: http://localhost:#{settings.port}/health"
  puts "   • API Info:     http://localhost:#{settings.port}/info"
  puts "   • DOI Analysis: http://localhost:#{settings.port}/summary?doi=<DOI>"
  puts ""
  puts "📚 Example Usage:"
  puts "   curl 'http://localhost:#{settings.port}/summary?doi=10.1016/j.kint.2021.02.040'"
  puts ""
  puts "🔧 Environment: #{settings.environment}"
  puts "📝 Log Level:   #{LOGGER.level}"
  puts "="*60
  puts ""
end
