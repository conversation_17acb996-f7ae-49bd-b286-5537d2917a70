# Abstract Essay API - Ruby Backend

Modern, temiz ve performanslı Ruby/Sinatra tabanlı akademik makale analiz API'si.

## 🚀 Özellikler

- **DOI Analizi**: Akademik makaleleri DOI ile analiz eder
- **PDF Erişimi**: Unpaywall API ile açık erişim PDF linklerini bulur
- **Paralel İşleme**: Analiz ve PDF sorguları paralel olarak çalışır
- **Hata Yönetimi**: Kapsamlı hata yakalama ve fallback mekanizmaları
- **CORS Desteği**: Frontend entegrasyonu için tam CORS desteği
- **Logging**: Detaylı loglama ve hata takibi
- **Performans**: Optimize edilmiş response süreleri

## 📋 Gereksinimler

- Ruby 3.0+
- Node.js 20+ (CLI agent için)
- Bundler gem

## 🛠️ Kurulum

1. **Bağımlılıkları yükle:**
   ```bash
   cd backend
   bundle install
   ```

2. **Environment dosyasını oluştur:**
   ```bash
   cp .env.example .env
   # .env dosyasını düzenle
   ```

3. **CLI agent bağımlılıklarını kontrol et:**
   ```bash
   cd ..
   npm install
   ```

## 🏃‍♂️ Çalıştırma

### Development Mode
```bash
cd backend
ruby app.rb
```

### Production Mode
```bash
cd backend
RACK_ENV=production ruby app.rb
```

### Puma ile çalıştırma
```bash
cd backend
bundle exec puma config.ru
```

## 📡 API Endpoints

### Health Check
```
GET /health
```
Sunucu durumunu kontrol eder.

### API Bilgisi
```
GET /info
```
API hakkında genel bilgi döner.

### DOI Analizi
```
GET /summary?doi=<DOI>
```
Verilen DOI için makale analizi yapar.

**Örnek:**
```bash
curl "http://localhost:4567/summary?doi=10.1016/j.kint.2021.02.040"
```

## 🧪 Test

### Temel testler
```bash
cd backend
ruby test-backend.rb
```

### Performans testleri
```bash
cd backend
ruby test-backend.rb --performance
```

## 📊 Response Formatı

```json
{
  "doi": "10.1016/j.kint.2021.02.040",
  "analysis": {
    "title": "Makale başlığı",
    "authors": "Yazar listesi",
    "journal": "Dergi adı",
    "year": "2021",
    "field": "Tıp",
    "abstract": "Makale özeti",
    "findings": "• Bulgular listesi",
    "conclusion": "Sonuç",
    "methodology": "Metodoloji",
    "methods": "Yöntemler",
    "dataQuality": "FULL|PARTIAL|LIMITED",
    "limitations": "Sınırlamalar",
    "keywords": "Anahtar kelimeler",
    "citationCount": 0,
    "pmid": ""
  },
  "pdf_access": {
    "is_open_access": true,
    "pdf_url": "https://example.com/paper.pdf",
    "host_type": "repository",
    "license": "cc-by"
  },
  "processing_time_seconds": 2.34,
  "processed_at": "2025-07-28T12:00:00Z",
  "api_version": "1.0"
}
```

## ⚙️ Konfigürasyon

Environment variables (.env dosyası):

```env
# Server Configuration
PORT=4567
BIND=0.0.0.0
RACK_ENV=development

# Logging
LOG_LEVEL=INFO

# External APIs
UNPAYWALL_EMAIL=<EMAIL>

# CLI Agent Configuration
CLI_TIMEOUT=30
```

## 🔧 Geliştirme

### Code Style
```bash
cd backend
bundle exec rubocop
```

### Auto-reload (development)
```bash
cd backend
bundle exec rerun ruby app.rb
```

## 📈 Performans

- Ortalama response süresi: ~2-5 saniye
- Paralel işleme ile optimize edilmiş
- Timeout koruması (30 saniye)
- Memory-efficient JSON parsing

## 🐛 Hata Ayıklama

1. **Server çalışmıyor:**
   ```bash
   ruby backend/app.rb
   ```

2. **Gem eksik:**
   ```bash
   cd backend && bundle install
   ```

3. **CLI agent çalışmıyor:**
   ```bash
   node cli-agent.js test-doi
   ```

4. **Log kontrolü:**
   ```bash
   LOG_LEVEL=DEBUG ruby backend/app.rb
   ```

## 🚀 Deployment

### Docker (opsiyonel)
```dockerfile
FROM ruby:3.0
WORKDIR /app
COPY backend/ .
RUN bundle install
EXPOSE 4567
CMD ["ruby", "app.rb"]
```

### Systemd Service
```ini
[Unit]
Description=Abstract Essay API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/backend
ExecStart=/usr/bin/ruby app.rb
Restart=always

[Install]
WantedBy=multi-user.target
```

## 📝 Changelog

### v1.0.0
- Modern Ruby/Sinatra backend
- Paralel işleme desteği
- Kapsamlı hata yönetimi
- Performans optimizasyonları
- Detaylı logging
- Comprehensive test suite
