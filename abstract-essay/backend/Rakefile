# frozen_string_literal: true

require 'rake'

desc 'Start the development server'
task :server do
  exec 'ruby app.rb'
end

desc 'Start the server with auto-reload'
task :dev do
  exec 'bundle exec rerun ruby app.rb'
end

desc 'Run tests'
task :test do
  ruby 'test-backend.rb'
end

desc 'Run performance tests'
task :perf do
  ruby 'test-backend.rb --performance'
end

desc 'Install dependencies'
task :install do
  sh 'bundle install'
end

desc 'Check code style'
task :lint do
  sh 'bundle exec rubocop'
end

desc 'Fix code style issues'
task :fix do
  sh 'bundle exec rubocop -a'
end

desc 'Show server info'
task :info do
  puts "🚀 Abstract Essay API - Ruby Backend"
  puts "="*50
  puts "📡 Endpoints:"
  puts "   Health: http://localhost:4567/health"
  puts "   Info:   http://localhost:4567/info"
  puts "   API:    http://localhost:4567/summary?doi=<DOI>"
  puts ""
  puts "🛠️  Commands:"
  puts "   rake server  - Start server"
  puts "   rake dev     - Start with auto-reload"
  puts "   rake test    - Run tests"
  puts "   rake perf    - Performance tests"
  puts "   rake lint    - Check code style"
end

task default: :info
