# 🔬 Academic Article Analyzer - New Architecture

Modern Ruby Sinatra backend + Next.js frontend ile akademik makale analiz sistemi.

## 🏗️ <PERSON><PERSON><PERSON>

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js       │    │   Ruby Sinatra  │    │   CLI Agent     │
│   Frontend      │◄──►│   Backend       │◄──►│   (Node.js)     │
│   (Port 3000)   │    │   (Port 4567)   │    │   + Mastra AI   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   Unpaywall     │
                       │   API           │
                       └─────────────────┘
```

## ✨ Özellikler

### 🎯 Backend (Ruby Sinatra)
- **RESTful API**: `GET /summary?doi=...` endpoint
- **CLI Agent Integration**: Node.js CLI agent'ı çalıştırır
- **Unpaywall API**: PDF erişim bilgisi
- **Error Handling**: <PERSON>psamlı hata yönetimi
- **CORS Support**: Frontend ile sorunsuz iletişim

### 🌐 Frontend (Next.js + TypeScript)
- **Modern UI**: Tailwind CSS ile responsive tasarım
- **Real-time Loading**: Animasyonlu loading states
- **SVG Generation**: Frontend'de dinamik SVG oluşturma
- **Error Handling**: <PERSON>llanı<PERSON><PERSON> dostu hata mesajları
- **PDF Download**: Açık erişim makaleler için PDF linki

### 🤖 AI Analysis
- **Multi-field Support**: Tüm akademik alanlar
- **Smart Field Detection**: Otomatik alan tespiti
- **Data Quality Assessment**: FULL/PARTIAL/LIMITED
- **Fallback Analysis**: Hata durumunda basit analiz

## 🚀 Kurulum

### Otomatik Kurulum
```bash
chmod +x setup.sh
./setup.sh
```

### Manuel Kurulum

#### 1. Ruby Backend
```bash
cd backend
bundle install
```

#### 2. Node.js Dependencies
```bash
npm install
```

#### 3. Next.js Frontend
```bash
cd frontend
npm install
```

## 🎮 Kullanım

### Otomatik Başlatma
```bash
chmod +x start-all.sh
./start-all.sh
```

### Manuel Başlatma

#### 1. Ruby Backend
```bash
cd backend
ruby app.rb
# http://localhost:4567
```

#### 2. Next.js Frontend
```bash
cd frontend
npm run dev
# http://localhost:3000
```

## 🧪 Test

### Backend Test
```bash
ruby backend/test-backend.rb
```

### CLI Agent Test
```bash
node test-cli-agent.js
```

### Frontend Test
Tarayıcıda `http://localhost:3000` adresine gidin ve DOI girin.

## 📡 API Endpoints

### Health Check
```
GET /health
```

### DOI Analysis
```
GET /summary?doi=10.1016/j.kint.2021.02.040
```

**Response:**
```json
{
  "doi": "10.1016/j.kint.2021.02.040",
  "analysis": {
    "title": "Article title",
    "authors": "Author names",
    "journal": "Journal name",
    "year": "2021",
    "field": "Tıp",
    "abstract": "Article abstract",
    "findings": "Key findings",
    "conclusion": "Conclusion",
    "methodology": "Methodology",
    "methods": "Methods used",
    "dataQuality": "PARTIAL",
    "limitations": "Analysis limitations",
    "keywords": "Keywords",
    "citationCount": 0,
    "pmid": ""
  },
  "pdf_access": {
    "is_open_access": true,
    "pdf_url": "https://...",
    "host_type": "publisher",
    "license": "cc-by"
  },
  "processed_at": "2024-01-01T12:00:00Z"
}
```

## 🎨 Frontend Özellikleri

### Responsive Design
- Mobile-first approach
- Tailwind CSS
- Modern UI components

### Interactive Elements
- DOI form with validation
- Loading animations
- Error handling
- Tab-based visualization

### SVG Generation
- Dynamic SVG creation
- Field-based color coding
- Downloadable graphics
- Responsive preview

## 🔧 Geliştirme

### Backend Geliştirme
```bash
cd backend
rerun ruby app.rb  # Auto-reload
```

### Frontend Geliştirme
```bash
cd frontend
npm run dev  # Hot reload
```

### CLI Agent Geliştirme
```bash
node cli-agent.js <DOI>  # Direct test
```

## 📁 Proje Yapısı

```
├── backend/
│   ├── Gemfile
│   ├── app.rb              # Ana Sinatra app
│   ├── config.ru           # Rack config
│   └── test-backend.rb     # Backend test
├── frontend/
│   ├── app/
│   │   ├── components/     # React bileşenleri
│   │   ├── globals.css     # Global styles
│   │   ├── layout.tsx      # Ana layout
│   │   └── page.tsx        # Ana sayfa
│   ├── package.json
│   ├── tailwind.config.js
│   └── tsconfig.json
├── cli-agent.js            # CLI agent
├── test-cli-agent.js       # CLI test
├── setup.sh                # Kurulum script
└── start-all.sh            # Başlatma script
```

## 🌟 Örnek DOI'ler

- **Tıp**: `10.1016/j.kint.2021.02.040`
- **Fizik**: `10.1038/s41586-021-03819-2`
- **Bilgisayar**: `10.1145/3447548.3467401`
- **Sosyal Bilimler**: `10.1007/s44163-022-00022-8`

## 🔍 Sorun Giderme

### Backend Çalışmıyor
```bash
# Ruby ve gem'leri kontrol et
ruby --version
bundle install
ruby backend/app.rb
```

### Frontend Çalışmıyor
```bash
# Node.js ve dependencies kontrol et
node --version
cd frontend && npm install
npm run dev
```

### CLI Agent Çalışmıyor
```bash
# Node.js dependencies kontrol et
npm install
node test-cli-agent.js
```

## 📊 Performans

- **Backend Response**: ~2-5 saniye
- **Frontend Render**: ~100ms
- **SVG Generation**: ~50ms
- **PDF Check**: ~1-2 saniye

## 🔒 Güvenlik

- CORS yapılandırması
- Input validation
- Error sanitization
- Rate limiting (opsiyonel)

## 🚀 Deployment

### Production Build
```bash
# Frontend
cd frontend
npm run build
npm start

# Backend
cd backend
bundle install --deployment
ruby app.rb -e production
```

## 📝 Lisans

MIT License - Detaylar için LICENSE dosyasına bakın.

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun
3. Commit edin
4. Push edin
5. Pull Request açın

## 📞 Destek

- Backend sorunları için Ruby/Sinatra dokümantasyonu
- Frontend sorunları için Next.js dokümantasyonu
- AI analizi için Mastra dokümantasyonu
