#!/bin/bash

echo "🚀 Starting Academic Article Analyzer"
echo "====================================="

# Function to kill background processes on exit
cleanup() {
    echo ""
    echo "🛑 Stopping all services..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit
}

# Set up trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Start Ruby Backend
echo "📡 Starting Ruby Backend (Port 4567)..."
cd backend
ruby app.rb &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Test backend
echo "🔍 Testing backend..."
curl -s http://localhost:4567/health > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ Backend is running"
else
    echo "❌ Backend failed to start"
    exit 1
fi

# Start Next.js Frontend
echo "🌐 Starting Next.js Frontend (Port 3000)..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo ""
echo "🎉 All services started!"
echo "📡 Backend API: http://localhost:4567"
echo "🌐 Frontend: http://localhost:3000"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user to stop
wait
