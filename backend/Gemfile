# frozen_string_literal: true

source 'https://rubygems.org'

ruby '~> 3.0'

# Web framework
gem 'sinatra', '~> 3.0'
gem 'sinatra-cors', '~> 1.2'

# HTTP client
gem 'httparty', '~> 0.21'

# JSON handling
gem 'json', '~> 2.6'

# Server
gem 'puma', '~> 6.0'
gem 'rackup', '~> 1.0'       # ← eski sürüm
gem 'rack', '< 3.0'          # ← sinatra uyumu için

# Logging
gem 'logger', '~> 1.5'

# Environment variables
gem 'dotenv', '~> 2.8'

# Validation
gem 'dry-validation', '~> 1.10'

# Background jobs (optional for future use)
gem 'sidekiq', '~> 7.0'

group :development do
  gem 'rerun', '~> 0.14'
  gem 'rubocop', '~> 1.50'
  gem 'rubocop-performance', '~> 1.18'
end

group :test do
  gem 'rspec', '~> 3.12'
  gem 'rack-test', '~> 2.1'
  gem 'webmock', '~> 3.18'
end
