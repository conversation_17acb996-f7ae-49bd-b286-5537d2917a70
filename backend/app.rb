# frozen_string_literal: true

require 'sinatra'
require 'sinatra/cors'
require 'json'
require 'httparty'
require 'net/http'
require 'uri'
require 'logger'
require 'dotenv/load'

# Configuration
configure do
  set :allow_origin, '*'
  set :allow_methods, 'GET,HEAD,POST,OPTIONS'
  set :allow_headers, 'content-type,if-modified-since,authorization'
  set :expose_headers, 'location,link'

  set :port, ENV.fetch('PORT', 4567)
  set :bind, ENV.fetch('BIND', '0.0.0.0')
  set :environment, ENV.fetch('RACK_ENV', 'development')

  set :json_encoder, :to_json
end

# Logger
LOGGER = Logger.new($stdout)
LOGGER.level = ENV.fetch('LOG_LEVEL', 'INFO').upcase.to_sym
LOGGER.formatter = proc do |severity, datetime, _, msg|
  "[#{datetime.strftime('%Y-%m-%d %H:%M:%S')}] #{severity}: #{msg}\n"
end

# Services
module Services
  class UnpaywallService
    include HTTParty
    base_uri 'https://api.unpaywall.org'
    TIMEOUT = 10
    DEFAULT_EMAIL = ENV.fetch('UNPAYWALL_EMAIL', '<EMAIL>')

    def self.get_pdf_link(doi, email = DEFAULT_EMAIL)
      LOGGER.info("Fetching PDF link for DOI: #{doi}")
      response = get("/v2/#{doi}?email=#{email}", timeout: TIMEOUT)

      if response.success?
        data = response.parsed_response
        if data['is_oa'] && data['best_oa_location']
          {
            is_open_access: true,
            pdf_url: data['best_oa_location']['url_for_pdf'],
            host_type: data['best_oa_location']['host_type'],
            license: data['best_oa_location']['license'],
            journal_name: data['journal_name'],
            publisher: data['publisher']
          }
        else
          { is_open_access: false, pdf_url: nil }
        end
      else
        { is_open_access: false, pdf_url: nil, error: "Unpaywall error: #{response.code}" }
      end
    rescue => e
      { is_open_access: false, pdf_url: nil, error: "Unpaywall error: #{e.message}" }
    end
  end

  class MastraService
    def self.analyze_doi(doi)
      LOGGER.info("Calling Mastra HTTP API for DOI: #{doi}")
      uri = URI("http://localhost:3000/api/analyze?doi=#{URI.encode_www_form_component(doi)}")
      res = Net::HTTP.get_response(uri)

      if res.is_a?(Net::HTTPSuccess)
        JSON.parse(res.body)
      else
        create_fallback_analysis(doi, "Mastra API error: #{res.code}")
      end
    rescue => e
      create_fallback_analysis(doi, e.message)
    end

    def self.create_fallback_analysis(doi, error_reason = 'Unknown error')
      LOGGER.warn("Fallback triggered for DOI: #{doi} (#{error_reason})")
      {
        'title' => 'Makale analizi yapılamadı',
        'authors' => 'Bilinmiyor',
        'journal' => 'Bilinmiyor',
        'year' => 'Bilinmiyor',
        'field' => 'Genel',
        'abstract' => 'Özet alınamadı',
        'findings' => '• Analiz yapılamadı\n• Lütfen daha sonra tekrar deneyin',
        'conclusion' => 'Sonuç analiz edilemedi',
        'methodology' => 'Metodoloji bilgisi alınamadı',
        'methods' => 'Yöntem bilgisi alınamadı',
        'dataQuality' => 'LIMITED',
        'limitations' => 'Sistem hatası nedeniyle analiz tamamlanamadı',
        'keywords' => '',
        'citationCount' => 0,
        'pmid' => '',
        'error' => error_reason,
        'fallback' => true
      }
    end
  end
end

# Helpers
helpers do
  def validate_doi(doi)
    doi && !doi.strip.empty? && doi.match?(/^10\.\d{4,}\/\S+$/)
  end

  def clean_doi_format(doi)
    doi.gsub(/^https?:\/\/(dx\.)?doi\.org\//, '').strip
  end

  def json_response(data, status_code = 200)
    content_type :json
    status status_code
    settings.development? ? JSON.pretty_generate(data) : data.to_json
  end

  def error_response(message, status_code = 400, details = nil)
    json_response({ error: message, details: details, timestamp: Time.now.iso8601 }, status_code)
  end
end

# Routes
get '/' do
  json_response({
    message: 'Abstract Essay API',
    version: '1.0',
    status: 'running',
    endpoints: {
      health: '/health',
      info: '/info',
      summary: '/summary?doi=<DOI>'
    },
    example: '/summary?doi=10.1016/j.kint.2021.02.040',
    timestamp: Time.now.iso8601
  })
end

get '/health' do
  json_response({
    status: 'OK',
    timestamp: Time.now.iso8601,
    version: '1.0',
    environment: settings.environment
  })
end

get '/info' do
  json_response({
    name: 'Abstract Essay API',
    version: '1.0',
    description: 'Academic paper analysis API using DOI',
    endpoints: {
      health: '/health',
      summary: '/summary?doi=<DOI>',
      info: '/info'
    }
  })
end

get '/summary' do
  doi = params['doi']
  return error_response('Geçerli bir DOI parametresi gerekli.') unless validate_doi(doi)

  clean_doi = clean_doi_format(doi)
  LOGGER.info("Processing summary for DOI: #{clean_doi}")

  begin
    start_time = Time.now
    analysis_thread = Thread.new { Services::MastraService.analyze_doi(clean_doi) }
    unpaywall_thread = Thread.new { Services::UnpaywallService.get_pdf_link(clean_doi) }

    analysis_result = analysis_thread.value
    unpaywall_result = unpaywall_thread.value

    json_response({
      doi: clean_doi,
      analysis: analysis_result,
      pdf_access: unpaywall_result,
      processing_time_seconds: (Time.now - start_time).round(2),
      processed_at: Time.now.iso8601,
      api_version: '1.0'
    })
  rescue => e
    error_response('Analiz sırasında hata oluştu', 500, { error: e.message })
  end
end

# CORS preflight
options '*' do
  response.headers['Allow'] = 'GET, POST, OPTIONS'
  response.headers['Access-Control-Allow-Headers'] = 'Authorization, Content-Type, Accept'
  response.headers['Access-Control-Allow-Origin'] = '*'
  200
end

# Error handlers
not_found do
  error_response('Endpoint bulunamadı', 404)
end

error do
  error_response('Sunucu hatası', 500, { error: env['sinatra.error'].message })
end

at_exit do
  LOGGER.info("Shutting down Abstract Essay API...")
end

if __FILE__ == $0
  puts "\n" + "="*60
  puts "🚀 Abstract Essay API - Ruby Backend"
  puts "="*60
  puts "📡 Endpoints:"
  puts "   • Health:  http://localhost:#{settings.port}/health"
  puts "   • Summary: http://localhost:#{settings.port}/summary?doi=<DOI>"
  puts "🔧 Mastra API: http://localhost:3000/api/analyze"
  puts "="*60
end
