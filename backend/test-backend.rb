#!/usr/bin/env ruby
# frozen_string_literal: true

require 'net/http'
require 'json'
require 'uri'

# Test the backend
class BackendTester
  BASE_URL = 'http://localhost:4567'

  def self.run_all_tests
    puts "\n" + "="*60
    puts "🧪 Abstract Essay API - Backend Tests"
    puts "="*60

    tests = [
      :test_health_endpoint,
      :test_info_endpoint,
      :test_summary_endpoint_valid_doi,
      :test_summary_endpoint_invalid_doi,
      :test_summary_endpoint_missing_doi,
      :test_cors_headers
    ]

    passed = 0
    failed = 0

    tests.each do |test_method|
      print "Running #{test_method.to_s.gsub('_', ' ').capitalize}... "

      begin
        if send(test_method)
          puts "✅ PASSED"
          passed += 1
        else
          puts "❌ FAILED"
          failed += 1
        end
      rescue => e
        puts "❌ ERROR: #{e.message}"
        failed += 1
      end
    end

    puts "\n" + "="*60
    puts "📊 Test Results: #{passed} passed, #{failed} failed"
    puts "="*60

    failed == 0
  end

  private

  def self.test_health_endpoint
    uri = URI("#{BASE_URL}/health")
    response = Net::HTTP.get_response(uri)

    return false unless response.code == '200'

    data = JSON.parse(response.body)
    data['status'] == 'OK' && data.key?('timestamp')
  end

  def self.test_info_endpoint
    uri = URI("#{BASE_URL}/info")
    response = Net::HTTP.get_response(uri)

    return false unless response.code == '200'

    data = JSON.parse(response.body)
    data.key?('name') && data.key?('endpoints')
  end

  def self.test_summary_endpoint_valid_doi
    test_doi = '10.1016/j.kint.2021.02.040'
    uri = URI("#{BASE_URL}/summary?doi=#{test_doi}")

    response = Net::HTTP.get_response(uri)
    return false unless response.code == '200'

    data = JSON.parse(response.body)
    data.key?('doi') && data.key?('analysis') && data.key?('pdf_access')
  end

  def self.test_summary_endpoint_invalid_doi
    uri = URI("#{BASE_URL}/summary?doi=invalid")
    response = Net::HTTP.get_response(uri)

    response.code == '400'
  end

  def self.test_summary_endpoint_missing_doi
    uri = URI("#{BASE_URL}/summary")
    response = Net::HTTP.get_response(uri)

    response.code == '400'
  end

  def self.test_cors_headers
    uri = URI("#{BASE_URL}/health")

    http = Net::HTTP.new(uri.host, uri.port)
    request = Net::HTTP::Options.new(uri)
    response = http.request(request)

    response.code == '200' &&
    response['Access-Control-Allow-Origin'] == '*'
  end
end

# Performance test
class PerformanceTester
  def self.run_performance_test(iterations = 5)
    puts "\n🚀 Performance Test (#{iterations} iterations)"
    puts "-" * 40

    test_doi = '10.1016/j.kint.2021.02.040'
    times = []

    iterations.times do |i|
      start_time = Time.now

      uri = URI("#{BackendTester::BASE_URL}/summary?doi=#{test_doi}")
      response = Net::HTTP.get_response(uri)

      end_time = Time.now
      duration = (end_time - start_time).round(2)
      times << duration

      status = response.code == '200' ? '✅' : '❌'
      puts "Request #{i+1}: #{duration}s #{status}"
    end

    avg_time = (times.sum / times.length).round(2)
    min_time = times.min.round(2)
    max_time = times.max.round(2)

    puts "-" * 40
    puts "📊 Performance Summary:"
    puts "   Average: #{avg_time}s"
    puts "   Min:     #{min_time}s"
    puts "   Max:     #{max_time}s"
  end
end

if __FILE__ == $0
  # Check if server is running
  begin
    uri = URI("#{BackendTester::BASE_URL}/health")
    Net::HTTP.get_response(uri)
  rescue Errno::ECONNREFUSED
    puts "❌ Server is not running. Please start the server first:"
    puts "   cd backend && ruby app.rb"
    exit 1
  end

  # Run tests
  success = BackendTester.run_all_tests

  if success && ARGV.include?('--performance')
    PerformanceTester.run_performance_test
  end

  if success
    puts "\n🎉 All tests passed!"
    puts "\n📝 Next steps:"
    puts "   1. Start frontend: cd frontend && npm run dev"
    puts "   2. Open browser: http://localhost:3000"
    puts "   3. Test with DOI input"
  else
    puts "\n💥 Some tests failed!"
    puts "\n🔧 Troubleshooting:"
    puts "   1. Is backend running? ruby backend/app.rb"
    puts "   2. Are gems installed? cd backend && bundle install"
    puts "   3. Is CLI agent working? node cli-agent.js test-doi"
  end

  exit(success ? 0 : 1)
end
