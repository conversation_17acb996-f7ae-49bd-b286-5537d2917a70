# Abstract Essay API Configuration

# OpenAI API Key (required for Mastra AI)
OPENAI_API_KEY=your_openai_api_key_here

# Server Configuration
PORT=4567
BIND=0.0.0.0
RACK_ENV=development

# Logging
LOG_LEVEL=INFO

# External APIs
UNPAYWALL_EMAIL=<EMAIL>

# CLI Agent Configuration
CLI_TIMEOUT=30

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:4567

# Optional: Redis for caching (future use)
# REDIS_URL=redis://localhost:6379

# Optional: Database (future use)
# DATABASE_URL=sqlite://db/abstract_essay.db

# Development Settings
NODE_ENV=development
