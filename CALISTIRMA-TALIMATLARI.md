# Academic Article Analyzer - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rma Talimatları

## 🚀 Hızlı Başlangıç

### G<PERSON><PERSON><PERSON>mler
- **Node.js** v20.9.0 veya üzeri
- **Ruby** v3.2.0 veya üzeri
- **Bundler** gem'i
- **OpenAI API Key** (isteğe bağlı - gelişmiş analiz için)

### 1. Environment Kurulumu

```bash
# .env dosyasını oluşturun (isteğe bağlı)
cp .env.example .env

# OpenAI API key'inizi ekleyin (isteğe bağlı)
# OPENAI_API_KEY=your_api_key_here
```

### 2. Dependencies Kurulumu

```bash
# Root dependencies (Mastra için)
npm install

# Backend dependencies
cd backend
bundle install
cd ..

# Frontend dependencies
cd frontend
npm install
cd ..
```

### 3. Servisleri Başlatma

#### Terminal 1: Backend Server
```bash
cd backend
bundle exec ruby app.rb
```
Backend http://localhost:4567 adresinde çalışacak.

#### Terminal 2: Frontend Server
```bash
cd frontend
npm run dev
```
Frontend http://localhost:3000 adresinde çalışacak.

### 4. Uygulamayı Kullanma

1. Tarayıcınızda http://localhost:3000 adresine gidin
2. DOI input alanına geçerli bir DOI girin (örn: `10.1016/j.kint.2021.02.040`)
3. "Analyze Article" butonuna tıklayın
4. Analiz sonuçlarını görüntüleyin

## 🧪 Test Çalıştırma

### Backend Testleri
```bash
ruby backend/test-backend.rb
```

### CLI Agent Testleri
```bash
node test-cli-agent.js
```

### Tek DOI Testi
```bash
node cli-agent.js "10.1016/j.kint.2021.02.040"
```

## 📁 Proje Yapısı

```
abstract-essay/
├── backend/                 # Ruby Sinatra API
│   ├── app.rb              # Ana server dosyası
│   ├── Gemfile             # Ruby dependencies
│   └── test-backend.rb     # Backend testleri
├── frontend/               # Next.js React uygulaması
│   ├── app/                # Next.js app directory
│   ├── package.json        # Frontend dependencies
│   └── next.config.js      # Next.js konfigürasyonu
├── src/mastra/             # Mastra AI konfigürasyonu
├── cli-agent.js            # CLI analiz aracı
├── test-cli-agent.js       # CLI agent testleri
└── package.json            # Root dependencies
```

## 🔧 API Endpoints

### Backend API (http://localhost:4567)

- `GET /health` - Sistem durumu
- `GET /info` - API bilgileri
- `GET /summary?doi=<DOI>` - Makale analizi

### Örnek API Kullanımı
```bash
curl "http://localhost:4567/summary?doi=10.1016/j.kint.2021.02.040"
```

## 🎯 Özellikler

### ✅ Çalışan Özellikler
- DOI doğrulama ve analiz
- CrossRef API entegrasyonu
- Unpaywall PDF erişim kontrolü
- Responsive web arayüzü
- Gerçek zamanlı analiz durumu
- Çoklu alan desteği (Tıp, Fizik, Bilgisayar, vb.)
- CORS desteği
- Hata yönetimi

### 🔄 Analiz Süreci
1. DOI doğrulama
2. CrossRef API'den metadata çekme
3. Alan belirleme (Tıp, Fizik, Bilgisayar, vb.)
4. Unpaywall API ile PDF erişim kontrolü
5. Sonuçları yapılandırma ve sunma

## 🛠️ Geliştirme

### Kod Yapısı
- **Backend**: Ruby Sinatra ile RESTful API
- **Frontend**: Next.js 15 + TypeScript + Tailwind CSS
- **CLI Agent**: Node.js ile CrossRef API entegrasyonu
- **Styling**: Tailwind CSS ile modern tasarım

### Önemli Dosyalar
- `backend/app.rb` - Ana API server
- `frontend/app/page.tsx` - Ana sayfa bileşeni
- `frontend/app/components/AnalysisResults.tsx` - Sonuç görüntüleme
- `cli-agent.js` - DOI analiz motoru

## 🔍 Sorun Giderme

### Backend Çalışmıyor
```bash
cd backend
bundle install
bundle exec ruby app.rb
```

### Frontend Çalışmıyor
```bash
cd frontend
npm install
npm run dev
```

### CLI Agent Hatası
```bash
# Dependencies kontrol edin
npm install
node cli-agent.js "test-doi"
```

### CORS Hatası
Backend server'ın çalıştığından emin olun (http://localhost:4567)

## 📊 Test Sonuçları

Tüm testler başarıyla geçiyor:
- ✅ Backend API testleri (6/6)
- ✅ CLI Agent testleri (3/3)
- ✅ Frontend build testi

## 🌐 Örnek DOI'ler

Test için kullanabileceğiniz DOI'ler:
- `10.1016/j.kint.2021.02.040` (Tıp)
- `10.1038/s41586-021-03819-2` (Fizik)
- `10.1145/3447548.3467401` (Bilgisayar Bilimleri)
- `10.1007/s44163-022-00022-8` (Sosyal Bilimler)

## 🎉 Başarılı Kurulum Kontrolü

Eğer aşağıdaki adımlar çalışıyorsa kurulum başarılıdır:
1. ✅ Backend Root: http://localhost:4567/ → API bilgileri JSON
2. ✅ Backend Health: http://localhost:4567/health → `{"status":"OK"}`
3. ✅ Frontend: http://localhost:3000 → Ana sayfa yükleniyor
4. ✅ CLI: `node cli-agent.js "10.1016/j.kint.2021.02.040"` → JSON çıktısı
5. ✅ Test: `ruby backend/test-backend.rb` → Tüm testler geçiyor

## 🔧 Sorun Giderme Güncellemesi

### ✅ Düzeltilen Sorunlar
- Backend root endpoint (/) artık doğru yanıt veriyor
- Tailwind CSS v3 stable sürümüne geçildi
- Next.js konfigürasyon uyarıları düzeltildi
- Viewport metadata uyarısı çözüldü
