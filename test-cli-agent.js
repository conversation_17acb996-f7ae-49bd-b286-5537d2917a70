#!/usr/bin/env node

/**
 * Test script for CLI Agent
 * Tests the CLI agent with various DOI inputs
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test DOIs
const testDOIs = [
  {
    doi: '10.1016/j.kint.2021.02.040',
    description: 'Medical/Kidney research paper',
    expectedField: 'Tıp'
  },
  {
    doi: '10.1038/s41586-021-03819-2',
    description: 'Nature Physics paper',
    expectedField: 'Fizik'
  },
  {
    doi: '10.1145/3447548.3467401',
    description: 'Computer Science ACM paper',
    expectedField: 'Bilgisayar'
  }
];

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCLIAgent(doi) {
  return new Promise((resolve, reject) => {
    const cliPath = join(__dirname, 'cli-agent.js');
    const child = spawn('node', [cliPath, doi], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({
        code,
        stdout: stdout.trim(),
        stderr: stderr.trim()
      });
    });

    child.on('error', (error) => {
      reject(error);
    });

    // Set timeout
    setTimeout(() => {
      child.kill('SIGTERM');
      reject(new Error('CLI agent timeout'));
    }, 45000); // 45 seconds timeout
  });
}

function validateResult(result, testCase) {
  const errors = [];
  
  try {
    const data = JSON.parse(result.stdout);
    
    // Check required fields
    const requiredFields = [
      'title', 'authors', 'journal', 'year', 'field',
      'abstract', 'findings', 'conclusion', 'methodology',
      'methods', 'dataQuality', 'limitations'
    ];
    
    for (const field of requiredFields) {
      if (!data[field] || data[field] === '') {
        errors.push(`Missing or empty field: ${field}`);
      }
    }
    
    // Check data quality
    const validDataQualities = ['FULL', 'PARTIAL', 'LIMITED'];
    if (!validDataQualities.includes(data.dataQuality)) {
      errors.push(`Invalid dataQuality: ${data.dataQuality}`);
    }
    
    // Check year format
    if (data.year && !/^\d{4}$/.test(data.year) && data.year !== 'Year not found') {
      errors.push(`Invalid year format: ${data.year}`);
    }
    
    return {
      valid: errors.length === 0,
      errors,
      data
    };
    
  } catch (parseError) {
    return {
      valid: false,
      errors: [`JSON parse error: ${parseError.message}`],
      data: null
    };
  }
}

async function runTests() {
  log('🧪 Starting CLI Agent Tests', 'bold');
  log('================================', 'blue');
  
  let passedTests = 0;
  let totalTests = testDOIs.length;
  
  for (let i = 0; i < testDOIs.length; i++) {
    const testCase = testDOIs[i];
    log(`\n📋 Test ${i + 1}/${totalTests}: ${testCase.description}`, 'blue');
    log(`DOI: ${testCase.doi}`, 'yellow');
    
    try {
      const startTime = Date.now();
      const result = await runCLIAgent(testCase.doi);
      const duration = Date.now() - startTime;
      
      log(`⏱️  Duration: ${duration}ms`, 'yellow');
      
      if (result.code === 0) {
        const validation = validateResult(result, testCase);
        
        if (validation.valid) {
          log('✅ Test PASSED', 'green');
          log(`   Title: ${validation.data.title.substring(0, 60)}...`, 'green');
          log(`   Field: ${validation.data.field}`, 'green');
          log(`   Data Quality: ${validation.data.dataQuality}`, 'green');
          passedTests++;
        } else {
          log('❌ Test FAILED - Validation errors:', 'red');
          validation.errors.forEach(error => {
            log(`   - ${error}`, 'red');
          });
        }
      } else {
        log(`❌ Test FAILED - Exit code: ${result.code}`, 'red');
        if (result.stderr) {
          log(`   Error: ${result.stderr}`, 'red');
        }
      }
      
    } catch (error) {
      log(`❌ Test FAILED - Exception: ${error.message}`, 'red');
    }
  }
  
  // Summary
  log('\n================================', 'blue');
  log('📊 Test Summary', 'bold');
  log(`Total Tests: ${totalTests}`, 'blue');
  log(`Passed: ${passedTests}`, passedTests === totalTests ? 'green' : 'yellow');
  log(`Failed: ${totalTests - passedTests}`, totalTests - passedTests === 0 ? 'green' : 'red');
  
  if (passedTests === totalTests) {
    log('\n🎉 All tests passed!', 'green');
    log('CLI Agent is working correctly.', 'green');
  } else {
    log('\n⚠️  Some tests failed!', 'yellow');
    log('Check the errors above and fix the CLI agent.', 'yellow');
  }
  
  return passedTests === totalTests;
}

// Handle command line arguments
if (process.argv.length > 2 && process.argv[2] !== '--run-tests') {
  // Single DOI test
  const doi = process.argv[2];
  log(`🔍 Testing single DOI: ${doi}`, 'blue');
  
  runCLIAgent(doi)
    .then(result => {
      if (result.code === 0) {
        log('✅ CLI Agent executed successfully', 'green');
        log('📄 Output:', 'blue');
        console.log(result.stdout);
      } else {
        log('❌ CLI Agent failed', 'red');
        log('📄 Error:', 'red');
        console.log(result.stderr);
      }
    })
    .catch(error => {
      log(`❌ Error: ${error.message}`, 'red');
      process.exit(1);
    });
} else {
  // Run all tests
  runTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`❌ Test runner error: ${error.message}`, 'red');
      process.exit(1);
    });
}
