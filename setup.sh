#!/bin/bash

echo "🚀 Academic Article Analyzer Setup"
echo "=================================="

# Ruby Backend Setup
echo ""
echo "📦 Setting up Ruby Backend..."
cd backend

# Check if Ruby is installed
if ! command -v ruby &> /dev/null; then
    echo "❌ Ruby is not installed. Please install Ruby first."
    exit 1
fi

# Check if <PERSON><PERSON><PERSON> is installed
if ! command -v bundle &> /dev/null; then
    echo "📦 Installing Bundler..."
    gem install bundler
fi

# Install Ruby gems
echo "📦 Installing Ruby gems..."
bundle install

cd ..

# Node.js CLI Agent Setup
echo ""
echo "📦 Setting up CLI Agent..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Install Node.js dependencies (if not already installed)
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

# Next.js Frontend Setup
echo ""
echo "📦 Setting up Next.js Frontend..."
cd frontend

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
npm install

cd ..

# Test CLI Agent
echo ""
echo "🧪 Testing CLI Agent..."
node cli-agent.js "10.1016/j.kint.2021.02.040" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ CLI Agent test passed"
else
    echo "⚠️  CLI Agent test failed, but continuing setup"
fi

# Create backend .env file if it doesn't exist
echo ""
echo "⚙️  Setting up configuration..."
if [ ! -f "backend/.env" ]; then
    cp backend/.env.example backend/.env
    echo "✅ Created backend/.env file"
fi

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "🚀 To start the application:"
echo "   Option 1 - Start all services:"
echo "     ./start-all.sh"
echo ""
echo "   Option 2 - Start manually:"
echo "     1. Backend:  cd backend && ruby app.rb"
echo "     2. Frontend: cd frontend && npm run dev"
echo "     3. Browser:  http://localhost:3000"
echo ""
echo "🧪 To test:"
echo "   - Backend test:     ruby backend/test-backend.rb"
echo "   - Performance test: ruby backend/test-backend.rb --performance"
echo "   - CLI agent test:   node cli-agent.js <DOI>"
echo ""
echo "📚 Documentation:"
echo "   - Backend README:   backend/README.md"
echo "   - API endpoints:    http://localhost:4567/info"
