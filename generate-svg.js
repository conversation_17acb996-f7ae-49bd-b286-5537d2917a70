#!/usr/bin/env node

/**
 * SVG Generator for Academic Article Analysis
 * Creates infographic-style SVG from analysis data
 */

import { SVG, registerWindow } from '@svgdotjs/svg.js';
import { createSVGWindow } from 'svgdom';

// Get command line arguments
const doi = process.argv[2];
const analysisDataJson = process.argv[3];

if (!doi || !analysisDataJson) {
  console.error('Usage: node generate-svg.js <DOI> <analysis_data_json>');
  process.exit(1);
}

let analysisData;
try {
  analysisData = JSON.parse(analysisDataJson);
} catch (error) {
  console.error('Invalid JSON data:', error.message);
  process.exit(1);
}

// Create SVG window
const window = createSVGWindow();
const document = window.document;
registerWindow(window, document);

// Create main SVG
const draw = SVG(document.documentElement).size(1000, 750);

// Background gradient
const gradient = draw.gradient('linear', (add) => {
  add.stop(0, '#f8f9fa')
  add.stop(1, '#e9ecef')
}).from(0, 0).to(0, 1);

draw.rect(1000, 750).fill(gradient);

// Field-based color selection
const fieldColors = {
  'Tıp': { primary: '#dc3545', secondary: '#c82333' },
  'Mühendislik': { primary: '#fd7e14', secondary: '#e8690b' },
  'Fizik': { primary: '#6f42c1', secondary: '#5a32a3' },
  'Kimya': { primary: '#20c997', secondary: '#1aa179' },
  'Bilgisayar': { primary: '#0d6efd', secondary: '#0b5ed7' },
  'Sosyal Bilimler': { primary: '#198754', secondary: '#157347' },
  'Matematik': { primary: '#6610f2', secondary: '#520dc2' },
  'Biyoloji': { primary: '#198754', secondary: '#157347' },
  'Genel': { primary: '#3d6a80', secondary: '#2c5364' }
};

const colors = fieldColors[analysisData.field] || fieldColors['Genel'];

// Header section
const headerGradient = draw.gradient('linear', (add) => {
  add.stop(0, colors.primary)
  add.stop(1, colors.secondary)
}).from(0, 0).to(1, 0);

draw.rect(1000, 80).fill(headerGradient);

// Title text - truncate if too long
const titleFontSize = analysisData.title.length > 80 ? 18 : 22;
const titleText = analysisData.title.length > 120 ? 
  analysisData.title.slice(0, 120) + '...' : analysisData.title;

draw.text(titleText)
  .move(20, 15)
  .font({ size: titleFontSize, weight: 'bold', family: 'Arial' })
  .fill('#ffffff')
  .width(960);

// Field label
draw.text(`📚 ${analysisData.field}`)
  .move(20, 50)
  .font({ size: 14, family: 'Arial', style: 'italic' })
  .fill('#ffffff');

// Content area
const contentY = 100;

// Left side - Methodology and Statistics
const leftX = 20;

// Data quality indicator
let qualityColor = '#dc3545'; // Red - LIMITED
if (analysisData.dataQuality === 'FULL') {
  qualityColor = '#198754'; // Green
} else if (analysisData.dataQuality === 'PARTIAL') {
  qualityColor = '#fd7e14'; // Orange
}

// Data quality box
draw.rect(300, 40).move(leftX, contentY).fill(qualityColor).radius(5);
draw.text(`Veri Kalitesi: ${analysisData.dataQuality}`)
  .move(leftX + 10, contentY + 10)
  .font({ size: 16, weight: 'bold', family: 'Arial' })
  .fill('#ffffff');

// Methodology box
draw.rect(300, 120).move(leftX, contentY + 50).fill('#e9ecef').radius(5);
draw.text('Metodoloji')
  .move(leftX + 10, contentY + 60)
  .font({ size: 18, weight: 'bold', family: 'Arial' })
  .fill('#212529');

const methodologyText = analysisData.methodology.length > 200 ? 
  analysisData.methodology.slice(0, 200) + '...' : analysisData.methodology;

draw.text(methodologyText)
  .move(leftX + 10, contentY + 90)
  .font({ size: 14, family: 'Arial' })
  .fill('#212529')
  .width(280);

// Methods box
draw.rect(300, 120).move(leftX, contentY + 180).fill('#e9ecef').radius(5);
const methodsTitle = analysisData.field === 'Tıp' ? 'İstatistiksel Analiz' :
                    analysisData.field === 'Mühendislik' ? 'Teknik Yöntemler' :
                    analysisData.field === 'Fizik' ? 'Deneysel/Teorik Yöntem' :
                    analysisData.field === 'Kimya' ? 'Analiz Yöntemleri' :
                    analysisData.field === 'Bilgisayar' ? 'Algoritma/Yöntem' :
                    'Kullanılan Yöntemler';

draw.text(methodsTitle)
  .move(leftX + 10, contentY + 190)
  .font({ size: 18, weight: 'bold', family: 'Arial' })
  .fill('#212529');

const methodsText = analysisData.methods.length > 200 ? 
  analysisData.methods.slice(0, 200) + '...' : analysisData.methods;

draw.text(methodsText)
  .move(leftX + 10, contentY + 220)
  .font({ size: 14, family: 'Arial' })
  .fill('#212529')
  .width(280);

// Article information
draw.rect(300, 160).move(leftX, contentY + 310).fill('#e9ecef').radius(5);
draw.text('Makale Bilgileri')
  .move(leftX + 10, contentY + 320)
  .font({ size: 18, weight: 'bold', family: 'Arial' })
  .fill('#212529');

// Authors - truncate if too long
const authorText = analysisData.authors.length > 50 ? 
  analysisData.authors.slice(0, 50) + '...' : analysisData.authors;

draw.text(`Yazarlar: ${authorText}`)
  .move(leftX + 10, contentY + 350)
  .font({ size: 14, family: 'Arial' })
  .fill('#212529')
  .width(280);

draw.text(`Dergi: ${analysisData.journal} (${analysisData.year})`)
  .move(leftX + 10, contentY + 375)
  .font({ size: 14, family: 'Arial' })
  .fill('#212529')
  .width(280);

// Citation and PMID information
if (analysisData.citationCount > 0 || analysisData.pmid) {
  let infoText = '';
  if (analysisData.citationCount > 0) {
    infoText += `Atıf: ${analysisData.citationCount}`;
  }
  if (analysisData.pmid) {
    if (infoText) infoText += ' | ';
    infoText += `PMID: ${analysisData.pmid}`;
  }

  draw.text(infoText)
    .move(leftX + 10, contentY + 400)
    .font({ size: 14, family: 'Arial' })
    .fill('#212529')
    .width(280);
}

// Keywords
if (analysisData.keywords) {
  const keywordsText = analysisData.keywords.length > 100 ? 
    analysisData.keywords.slice(0, 100) + '...' : analysisData.keywords;
  
  draw.text(`Anahtar Kelimeler: ${keywordsText}`)
    .move(leftX + 10, contentY + 425)
    .font({ size: 12, family: 'Arial', style: 'italic' })
    .fill('#6c757d')
    .width(280);
}

// Right side - Abstract, Findings and Conclusion
const rightX = 340;

// Abstract box
const abstractBg = draw.rect(640, 150).move(rightX, contentY).fill('#d1e7dd').radius(5);
draw.text('Özet')
  .move(rightX + 10, contentY + 10)
  .font({ size: 18, weight: 'bold', family: 'Arial' })
  .fill('#0f5132');

// Check if abstract is available
if (analysisData.abstract === 'Abstract not available - please refer to the original publication' || 
    analysisData.abstract === 'Özet mevcut değil') {
  abstractBg.fill('#f8d7da');
  draw.text('Özet')
    .move(rightX + 10, contentY + 10)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#842029');

  draw.text('Makale özeti bulunamadı. Analiz başlık ve metadata bilgilerine dayanmaktadır.')
    .move(rightX + 10, contentY + 40)
    .font({ size: 14, family: 'Arial', style: 'italic' })
    .fill('#842029')
    .width(620);
} else {
  const abstractText = analysisData.abstract.length > 400 ? 
    analysisData.abstract.slice(0, 400) + '...' : analysisData.abstract;
  
  draw.text(abstractText)
    .move(rightX + 10, contentY + 40)
    .font({ size: 14, family: 'Arial' })
    .fill('#0f5132')
    .width(620);
}

// Main findings box
draw.rect(640, 180).move(rightX, contentY + 170).fill('#cfe2ff').radius(5);
draw.text('Ana Bulgular')
  .move(rightX + 10, contentY + 180)
  .font({ size: 18, weight: 'bold', family: 'Arial' })
  .fill('#084298');

// Findings - line by line
const findingsText = analysisData.findings.length > 500 ? 
  analysisData.findings.slice(0, 500) + '...' : analysisData.findings;

const findingsLines = findingsText.split('\n');
let yOffset = 0;
findingsLines.slice(0, 6).forEach((line, index) => { // Max 6 lines
  draw.text(line)
    .move(rightX + 10, contentY + 210 + yOffset)
    .font({ size: 14, family: 'Arial' })
    .fill('#084298')
    .width(620);

  yOffset += 25; // 25px spacing per line
});

// Conclusion box
draw.rect(640, 130).move(rightX, contentY + 370).fill('#f8d7da').radius(5);
draw.text('Sonuç')
  .move(rightX + 10, contentY + 380)
  .font({ size: 18, weight: 'bold', family: 'Arial' })
  .fill('#842029');

const conclusionText = analysisData.conclusion.length > 300 ? 
  analysisData.conclusion.slice(0, 300) + '...' : analysisData.conclusion;

draw.text(conclusionText)
  .move(rightX + 10, contentY + 410)
  .font({ size: 14, family: 'Arial' })
  .fill('#842029')
  .width(620);

// Data quality warning
if (analysisData.dataQuality !== 'FULL') {
  draw.rect(980, 40).move(10, 690).fill('#fff3cd').radius(5);
  draw.text('⚠️ Uyarı: Bu özet sınırlı verilerle oluşturulmuştur. Bazı bilgiler tahmin edilmiş olabilir.')
    .move(20, 700)
    .font({ size: 14, family: 'Arial', weight: 'bold' })
    .fill('#664d03')
    .width(960);
}

// Footer disclaimer
const disclaimer = analysisData.field === 'Tıp' ?
  'Bu özet yapay zeka tarafından oluşturulmuştur. Klinik kararlar için orijinal makaleye başvurunuz.' :
  'Bu özet yapay zeka tarafından oluşturulmuştur. Detaylı bilgi için orijinal makaleye başvurunuz.';

draw.text(disclaimer)
  .move(20, 730)
  .font({ size: 12, family: 'Arial', style: 'italic' })
  .fill('#6c757d');

// Output SVG
console.log(draw.svg());
