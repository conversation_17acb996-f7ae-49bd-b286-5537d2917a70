import { openai } from '@ai-sdk/openai';
// Tip eksikliği için geçici çözüm
declare module 'svgdom';
import { Agent } from '@mastra/core/agent';
import { Memory } from '@mastra/memory';
import { LibSQLStore } from '@mastra/libsql';
import { articleSummaryTool } from '../tools/article-summary-tool';
import { SVG, registerWindow } from '@svgdotjs/svg.js';
import { createSVGWindow } from 'svgdom';

// Gelişmiş SVG oluşturucu fonksiyon - Tüm akademik alanlar için
export function createAcademicSummarySVG({
  title,
  authors,
  journal,
  year,
  field = 'Genel',
  abstract,
  findings,
  conclusion,
  methodology,
  methods = 'Belirtilmemiş',
  dataQuality = 'PARTIAL',
  keywords = '',
  citationCount = 0,
  pmid = ''
}: {
  title: string;
  authors: string;
  journal: string;
  year: string | number;
  field?: string;
  abstract: string;
  findings: string;
  conclusion: string;
  methodology: string;
  methods?: string;
  dataQuality?: 'FULL' | 'PARTIAL' | 'LIMITED' | string;
  keywords?: string;
  citationCount?: number;
  pmid?: string;
}) {
  const window = createSVGWindow();
  const document = window.document;
  registerWindow(window, document);

  // Ana SVG oluştur
  const svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  document.body.appendChild(svgElement);
  const draw = SVG().addTo(svgElement).size(1000, 750);

  // Arkaplan gradyanı
  const gradient = draw.gradient('linear', (add: any) => {
    add.stop(0, '#f8f9fa')
    add.stop(1, '#e9ecef')
  }).from(0, 0).to(0, 1);

  draw.rect(1000, 750).fill(gradient);

  // Alan bazlı renk seçimi
  const fieldColors: { [key: string]: { primary: string; secondary: string } } = {
    'Tıp': { primary: '#dc3545', secondary: '#c82333' },
    'Mühendislik': { primary: '#fd7e14', secondary: '#e8690b' },
    'Fizik': { primary: '#6f42c1', secondary: '#5a32a3' },
    'Kimya': { primary: '#20c997', secondary: '#1aa179' },
    'Bilgisayar': { primary: '#0d6efd', secondary: '#0b5ed7' },
    'Sosyal Bilimler': { primary: '#198754', secondary: '#157347' },
    'Matematik': { primary: '#6610f2', secondary: '#520dc2' },
    'Biyoloji': { primary: '#198754', secondary: '#157347' },
    'Genel': { primary: '#3d6a80', secondary: '#2c5364' }
  };

  const colors = fieldColors[field] || fieldColors['Genel'];

  // Başlık alanı
  const headerGradient = draw.gradient('linear', (add: any) => {
    add.stop(0, colors.primary)
    add.stop(1, colors.secondary)
  }).from(0, 0).to(1, 0);

  const headerBg = draw.rect(1000, 80).fill(headerGradient);

  // Başlık metni - uzunsa küçült
  const titleFontSize = title.length > 80 ? 18 : 22;
  draw.text(title.slice(0, 120) + (title.length > 120 ? '...' : ''))
    .move(20, 15)
    .font({ size: titleFontSize, weight: 'bold', family: 'Arial' })
    .fill('#ffffff')
    .width(960);

  // Alan etiketi
  draw.text(`📚 ${field}`)
    .move(20, 50)
    .font({ size: 14, family: 'Arial', style: 'italic' })
    .fill('#ffffff');

  // İçerik alanı
  const contentY = 100;

  // Sol taraf - Metodoloji ve İstatistik
  const leftX = 20;

  // Veri kalitesi göstergesi
  let qualityColor = '#dc3545'; // Kırmızı - LIMITED
  if (dataQuality === 'FULL') {
    qualityColor = '#198754'; // Yeşil
  } else if (dataQuality === 'PARTIAL') {
    qualityColor = '#fd7e14'; // Turuncu
  }

  // Veri kalitesi kutusu
  draw.rect(300, 40).move(leftX, contentY).fill(qualityColor).radius(5);
  draw.text(`Veri Kalitesi: ${dataQuality}`)
    .move(leftX + 10, contentY + 10)
    .font({ size: 16, weight: 'bold', family: 'Arial' })
    .fill('#ffffff');

  // Metodoloji kutusu
  draw.rect(300, 120).move(leftX, contentY + 50).fill('#e9ecef').radius(5);
  draw.text('Metodoloji')
    .move(leftX + 10, contentY + 60)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#212529');

  draw.text(methodology)
    .move(leftX + 10, contentY + 90)
    .font({ size: 14, family: 'Arial' })
    .fill('#212529')
    .width(280);

  // Yöntemler kutusu (alan bazlı)
  draw.rect(300, 120).move(leftX, contentY + 180).fill('#e9ecef').radius(5);
  const methodsTitle = field === 'Tıp' ? 'İstatistiksel Analiz' :
    field === 'Mühendislik' ? 'Teknik Yöntemler' :
      field === 'Fizik' ? 'Deneysel/Teorik Yöntem' :
        field === 'Kimya' ? 'Analiz Yöntemleri' :
          field === 'Bilgisayar' ? 'Algoritma/Yöntem' :
            'Kullanılan Yöntemler';

  draw.text(methodsTitle)
    .move(leftX + 10, contentY + 190)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#212529');

  draw.text(methods)
    .move(leftX + 10, contentY + 220)
    .font({ size: 14, family: 'Arial' })
    .fill('#212529')
    .width(280);

  // Makale bilgileri
  draw.rect(300, 160).move(leftX, contentY + 310).fill('#e9ecef').radius(5);
  draw.text('Makale Bilgileri')
    .move(leftX + 10, contentY + 320)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#212529');

  // Yazarlar - uzunsa kısalt
  const authorText = authors.length > 50 ? authors.slice(0, 50) + '...' : authors;
  draw.text(`Yazarlar: ${authorText}`)
    .move(leftX + 10, contentY + 350)
    .font({ size: 14, family: 'Arial' })
    .fill('#212529')
    .width(280);

  draw.text(`Dergi: ${journal} (${year})`)
    .move(leftX + 10, contentY + 375)
    .font({ size: 14, family: 'Arial' })
    .fill('#212529')
    .width(280);

  // Atıf ve PMID bilgileri
  if (citationCount > 0 || pmid) {
    let infoText = '';
    if (citationCount > 0) {
      infoText += `Atıf: ${citationCount}`;
    }
    if (pmid) {
      if (infoText) infoText += ' | ';
      infoText += `PMID: ${pmid}`;
    }

    draw.text(infoText)
      .move(leftX + 10, contentY + 400)
      .font({ size: 14, family: 'Arial' })
      .fill('#212529')
      .width(280);
  }

  // Anahtar kelimeler
  if (keywords) {
    draw.text(`Anahtar Kelimeler: ${keywords.slice(0, 100)}...`)
      .move(leftX + 10, contentY + 425)
      .font({ size: 12, family: 'Arial', style: 'italic' })
      .fill('#6c757d')
      .width(280);
  }

  // Sağ taraf - Özet, Bulgular ve Sonuç
  const rightX = 340;

  // Özet kutusu
  const abstractBg = draw.rect(640, 150).move(rightX, contentY).fill('#d1e7dd').radius(5);
  draw.text('Özet')
    .move(rightX + 10, contentY + 10)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#0f5132');

  // Özet yoksa uyarı göster
  if (abstract === 'Özet bulunamadı' || abstract === 'Özet mevcut değil') {
    abstractBg.fill('#f8d7da');
    draw.text('Özet')
      .move(rightX + 10, contentY + 10)
      .font({ size: 18, weight: 'bold', family: 'Arial' })
      .fill('#842029');

    draw.text('Makale özeti bulunamadı. Analiz başlık ve metadata bilgilerine dayanmaktadır.')
      .move(rightX + 10, contentY + 40)
      .font({ size: 14, family: 'Arial', style: 'italic' })
      .fill('#842029')
      .width(620);
  } else {
    draw.text(abstract.slice(0, 400) + (abstract.length > 400 ? '...' : ''))
      .move(rightX + 10, contentY + 40)
      .font({ size: 14, family: 'Arial' })
      .fill('#0f5132')
      .width(620);
  }

  // Ana Bulgular kutusu
  draw.rect(640, 180).move(rightX, contentY + 170).fill('#cfe2ff').radius(5);
  draw.text('Ana Bulgular')
    .move(rightX + 10, contentY + 180)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#084298');

  // Bulgular - satır satır göster
  const findingsLines = findings.split('\n');
  let yOffset = 0;
  findingsLines.forEach((line: string, index: number) => {
    draw.text(line)
      .move(rightX + 10, contentY + 210 + yOffset)
      .font({ size: 14, family: 'Arial' })
      .fill('#084298')
      .width(620);

    yOffset += 25; // Her satır için 25px boşluk
  });

  // Sonuç kutusu
  draw.rect(640, 130).move(rightX, contentY + 370).fill('#f8d7da').radius(5);
  draw.text('Sonuç')
    .move(rightX + 10, contentY + 380)
    .font({ size: 18, weight: 'bold', family: 'Arial' })
    .fill('#842029');

  draw.text(conclusion)
    .move(rightX + 10, contentY + 410)
    .font({ size: 14, family: 'Arial' })
    .fill('#842029')
    .width(620);

  // Veri kalitesi uyarısı
  if (dataQuality !== 'FULL') {
    draw.rect(980, 40).move(10, 690).fill('#fff3cd').radius(5);
    draw.text('⚠️ Uyarı: Bu özet sınırlı verilerle oluşturulmuştur. Bazı bilgiler tahmin edilmiş olabilir.')
      .move(20, 700)
      .font({ size: 14, family: 'Arial', weight: 'bold' })
      .fill('#664d03')
      .width(960);
  }

  // Alt bilgi
  const disclaimer = field === 'Tıp' ?
    'Bu özet yapay zeka tarafından oluşturulmuştur. Klinik kararlar için orijinal makaleye başvurunuz.' :
    'Bu özet yapay zeka tarafından oluşturulmuştur. Detaylı bilgi için orijinal makaleye başvurunuz.';

  draw.text(disclaimer)
    .move(20, 730)
    .font({ size: 12, family: 'Arial', style: 'italic' })
    .fill('#6c757d');

  return draw.svg();
}

export const articleSummaryAgent = new Agent({
  name: 'Universal Academic Article Analyzer',
  instructions: `
    Sen gelişmiş bir akademik makale analiz uzmanısın. Kullanıcı sana herhangi bir alandaki makale DOI'si verecek.

    GÖREV ADIMLARI:
    1. articleSummaryTool ile makale verilerini çek (başlık, yazarlar, dergi, yıl, özet, anahtar kelimeler)
    2. Makale alanını belirle (tıp, mühendislik, sosyal bilimler, fizik, kimya, bilgisayar bilimi, vb.)
    3. Mevcut verileri kullanarak akıllı analiz yap:

    EĞER ÖZET MEVCUT İSE:
    - Özetten metodoloji bilgilerini çıkar
    - Ana bulguları tespit et
    - Sonuçları özetle
    - Kullanılan yöntemleri belirle

    EĞER ÖZET MEVCUT DEĞİLSE:
    - Başlık ve anahtar kelimelerden makale türünü ve alanını belirle
    - Dergi ve yayın yılından makale kalitesini değerlendir
    - Alan bilgisiyle makul tahminler yap
    - Eksik bilgileri açıkça belirt

    ÇIKTI FORMATI (JSON):
    {
      "title": "Tam makale başlığı",
      "authors": "Yazar listesi",
      "journal": "Dergi adı",
      "year": "Yayın yılı",
      "field": "Makale alanı (örn: Tıp, Mühendislik, Fizik, vb.)",
      "abstract": "Mevcut özet veya 'Özet mevcut değil' mesajı",
      "findings": "• Madde 1\\n• Madde 2\\n• Madde 3 (mevcut bilgilere dayalı)",
      "conclusion": "Sonuç özeti (mevcut bilgilere dayalı)",
      "methodology": "Metodoloji açıklaması (mevcut bilgilere dayalı)",
      "methods": "Kullanılan yöntemler (deneysel, teorik, simülasyon, vb.)",
      "dataQuality": "FULL/PARTIAL/LIMITED - veri kalitesi",
      "limitations": "Analiz sınırlamaları",
      "keywords": "Anahtar kelimeler",
      "citationCount": "Atıf sayısı (varsa)",
      "pmid": "PubMed ID (varsa)"
    }

    ALAN ÖRNEKLERI VE YAKLAŞIMLAR:
    - TIP: Hasta grupları, tedavi yöntemleri, klinik sonuçlar
    - MÜHENDİSLİK: Tasarım, performans, optimizasyon
    - FİZİK: Teorik modeller, deneysel sonuçlar, simülasyonlar
    - KİMYA: Sentez, analiz, reaksiyon mekanizmaları
    - BİLGİSAYAR: Algoritmalar, performans, karşılaştırmalar
    - SOSYAL BİLİMLER: Anket, gözlem, istatistiksel analiz

    ÖNEMLİ NOTLAR:
    - Mevcut olmayan bilgileri uydurma, tahmin ettiğini belirt
    - Veri kalitesini açıkça belirt
    - Her alan için uygun terminoloji kullan
    - Eksik bilgiler için alternatif kaynaklar öner
    - Sadece makale analizi yap, tavsiye verme

    ÖRNEK CEVAP:
    Makale verilerini analiz ettim. İşte bulgularım:

    [JSON formatında sonuçlar]

    Alan: [Makale alanı]
    Veri Kalitesi: [FULL/PARTIAL/LIMITED]
    Sınırlamalar: [Eksik bilgiler ve tahminler]

    Daha detaylı analiz için makaleye doğrudan erişim önerilir.
  `,
  model: openai('gpt-4o-mini'),
  tools: { articleSummaryTool },
  memory: new Memory({
    storage: new LibSQLStore({
      url: 'file:../mastra.db',
    }),
  }),
});