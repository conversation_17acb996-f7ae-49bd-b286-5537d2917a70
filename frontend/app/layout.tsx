import type { Metadata } from 'next';
import './globals.css';

export const metadata: Metadata = {
  title: 'Academic Article Analyzer',
  description: 'Modern academic article analysis system with AI-powered insights',
  keywords: ['academic', 'research', 'DOI', 'analysis', 'AI', 'article'],
  authors: [{ name: 'Academic Research Team' }],
  robots: 'index, follow',
  openGraph: {
    title: 'Academic Article Analyzer',
    description: 'AI-powered academic article analysis system',
    type: 'website',
    locale: 'tr_TR',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Academic Article Analyzer',
    description: 'AI-powered academic article analysis system',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="tr" className="h-full">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#3b82f6" />
      </head>
      <body className="h-full bg-gradient-to-br from-blue-50 via-white to-purple-50 antialiased">
        <div className="min-h-full">
          <header className="bg-white shadow-sm border-b border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center py-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">AA</span>
                  </div>
                  <div>
                    <h1 className="text-xl font-bold text-gray-900">
                      Academic Article Analyzer
                    </h1>
                    <p className="text-sm text-gray-500">
                      AI-powered research analysis
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="hidden sm:flex items-center space-x-2 text-sm text-gray-500">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>System Active</span>
                  </div>
                </div>
              </div>
            </div>
          </header>

          <main className="flex-1">
            {children}
          </main>

          <footer className="bg-white border-t border-gray-200 mt-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">
                    About
                  </h3>
                  <p className="text-sm text-gray-600">
                    Modern academic article analysis system powered by AI.
                    Analyze research papers using DOI numbers.
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">
                    Features
                  </h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• AI-powered analysis</li>
                    <li>• Multi-field support</li>
                    <li>• Real-time processing</li>
                    <li>• PDF access detection</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">
                    Technology
                  </h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Next.js Frontend</li>
                    <li>• Ruby Sinatra Backend</li>
                    <li>• Mastra AI Integration</li>
                    <li>• Unpaywall API</li>
                  </ul>
                </div>
              </div>
              <div className="mt-8 pt-8 border-t border-gray-200">
                <p className="text-center text-sm text-gray-500">
                  © 2024 Academic Article Analyzer. Built with modern web technologies.
                </p>
              </div>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
}
