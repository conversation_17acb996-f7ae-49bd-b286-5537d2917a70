'use client';

import { useState } from 'react';
import { Download, ExternalLink, CheckCircle, Clock, AlertCircle, FileText, Users, Calendar, Tag } from 'lucide-react';

interface AnalysisResult {
  doi: string;
  analysis: {
    title: string;
    authors: string;
    journal: string;
    year: string;
    field: string;
    abstract: string;
    findings: string;
    conclusion: string;
    methodology: string;
    methods: string;
    dataQuality: string;
    limitations: string;
    keywords: string;
    citationCount: number;
    pmid: string;
  };
  pdf_access: {
    is_open_access: boolean;
    pdf_url?: string;
    host_type?: string;
    license?: string;
  };
  processing_time_seconds: number;
  processed_at: string;
}

interface AnalysisResultsProps {
  result: AnalysisResult;
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export default function AnalysisResults({ result, activeTab, setActiveTab }: AnalysisResultsProps) {
  const getFieldColor = (field: string): string => {
    const colors: { [key: string]: string } = {
      'Tıp': 'bg-red-100 text-red-800',
      'Fizik': 'bg-blue-100 text-blue-800',
      '<PERSON>ya': 'bg-green-100 text-green-800',
      'Bilgisayar': 'bg-purple-100 text-purple-800',
      'Mühendislik': 'bg-orange-100 text-orange-800',
      'Sosyal Bilimler': 'bg-pink-100 text-pink-800',
      'Matematik': 'bg-indigo-100 text-indigo-800',
      'Biyoloji': 'bg-emerald-100 text-emerald-800',
      'Genel': 'bg-gray-100 text-gray-800',
    };
    return colors[field] || colors['Genel'];
  };

  const getDataQualityInfo = (quality: string) => {
    const info = {
      'FULL': { color: 'text-green-600', icon: CheckCircle, text: 'Full Analysis' },
      'PARTIAL': { color: 'text-yellow-600', icon: Clock, text: 'Partial Analysis' },
      'LIMITED': { color: 'text-red-600', icon: AlertCircle, text: 'Limited Analysis' },
    };
    return info[quality as keyof typeof info] || info['LIMITED'];
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FileText },
    { id: 'methodology', label: 'Methodology', icon: Users },
    { id: 'findings', label: 'Findings', icon: CheckCircle },
    { id: 'access', label: 'PDF Access', icon: Download },
  ];

  const qualityInfo = getDataQualityInfo(result.analysis.dataQuality);
  const QualityIcon = qualityInfo.icon;

  return (
    <div className="space-y-6">
      {/* Article Header */}
      <div className="card">
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-gray-900 mb-3 leading-tight">
              {result.analysis.title}
            </h2>
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-1" />
                <span>{result.analysis.authors}</span>
              </div>
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-1" />
                <span>{result.analysis.journal}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                <span>{result.analysis.year}</span>
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-3">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getFieldColor(result.analysis.field)}`}>
                {result.analysis.field}
              </span>
              <div className={`flex items-center space-x-1 ${qualityInfo.color}`}>
                <QualityIcon className="h-4 w-4" />
                <span className="text-sm font-medium">{qualityInfo.text}</span>
              </div>
              {result.analysis.citationCount > 0 && (
                <span className="text-sm text-gray-500">
                  {result.analysis.citationCount} citations
                </span>
              )}
            </div>
          </div>
          
          {/* PDF Access */}
          {result.pdf_access.is_open_access && result.pdf_access.pdf_url && (
            <div className="flex-shrink-0">
              <a
                href={result.pdf_access.pdf_url}
                target="_blank"
                rel="noopener noreferrer"
                className="btn-primary flex items-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>Download PDF</span>
                <ExternalLink className="h-3 w-3" />
              </a>
              <p className="text-xs text-gray-500 mt-1 text-center">
                {result.pdf_access.license && `License: ${result.pdf_access.license}`}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'tab-button active'
                    : 'tab-button inactive'
                } flex items-center space-x-2`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Abstract</h3>
              <p className="text-gray-700 leading-relaxed">
                {result.analysis.abstract}
              </p>
            </div>
            
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Conclusion</h3>
              <p className="text-gray-700 leading-relaxed">
                {result.analysis.conclusion}
              </p>
            </div>
            
            {result.analysis.keywords && (
              <div className="card lg:col-span-2">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Tag className="h-5 w-5 mr-2" />
                  Keywords
                </h3>
                <div className="flex flex-wrap gap-2">
                  {result.analysis.keywords.split(',').map((keyword, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm"
                    >
                      {keyword.trim()}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'methodology' && (
          <div className="space-y-6">
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Methodology</h3>
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {result.analysis.methodology}
              </p>
            </div>
            
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Methods Used</h3>
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {result.analysis.methods}
              </p>
            </div>
            
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Limitations</h3>
              <p className="text-gray-700 leading-relaxed">
                {result.analysis.limitations}
              </p>
            </div>
          </div>
        )}

        {activeTab === 'findings' && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Findings</h3>
            <div className="prose prose-gray max-w-none">
              <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                {result.analysis.findings}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'access' && (
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">PDF Access Information</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">Open Access Status</p>
                  <p className="text-sm text-gray-600">
                    {result.pdf_access.is_open_access ? 'Available' : 'Not Available'}
                  </p>
                </div>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  result.pdf_access.is_open_access 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {result.pdf_access.is_open_access ? 'Open Access' : 'Restricted'}
                </div>
              </div>
              
              {result.pdf_access.is_open_access && (
                <>
                  {result.pdf_access.pdf_url && (
                    <div className="p-4 bg-green-50 rounded-lg">
                      <p className="font-medium text-green-900 mb-2">PDF Download Available</p>
                      <a
                        href={result.pdf_access.pdf_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-green-700 hover:text-green-800 underline flex items-center"
                      >
                        {result.pdf_access.pdf_url}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </a>
                    </div>
                  )}
                  
                  {result.pdf_access.host_type && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Host Type:</span>
                      <span className="font-medium">{result.pdf_access.host_type}</span>
                    </div>
                  )}
                  
                  {result.pdf_access.license && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">License:</span>
                      <span className="font-medium">{result.pdf_access.license}</span>
                    </div>
                  )}
                </>
              )}
              
              <div className="pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>DOI:</span>
                  <span className="font-mono">{result.doi}</span>
                </div>
                {result.analysis.pmid && (
                  <div className="flex justify-between items-center text-sm text-gray-500 mt-2">
                    <span>PMID:</span>
                    <span className="font-mono">{result.analysis.pmid}</span>
                  </div>
                )}
                <div className="flex justify-between items-center text-sm text-gray-500 mt-2">
                  <span>Processing Time:</span>
                  <span>{result.processing_time_seconds}s</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
