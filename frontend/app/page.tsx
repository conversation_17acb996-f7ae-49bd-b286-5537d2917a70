'use client';

import { useState } from 'react';
import { Search, FileText, Download, AlertCircle, CheckCircle, Clock, Loader2 } from 'lucide-react';
import axios from 'axios';
import AnalysisResults from './components/AnalysisResults';

interface AnalysisResult {
  doi: string;
  analysis: {
    title: string;
    authors: string;
    journal: string;
    year: string;
    field: string;
    abstract: string;
    findings: string;
    conclusion: string;
    methodology: string;
    methods: string;
    dataQuality: string;
    limitations: string;
    keywords: string;
    citationCount: number;
    pmid: string;
  };
  pdf_access: {
    is_open_access: boolean;
    pdf_url?: string;
    host_type?: string;
    license?: string;
  };
  processing_time_seconds: number;
  processed_at: string;
}

interface LoadingState {
  isLoading: boolean;
  stage: string;
  progress: number;
}

export default function HomePage() {
  const [doi, setDoi] = useState('');
  const [result, setResult] = useState<AnalysisResult | null>(null);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: false,
    stage: '',
    progress: 0
  });
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');

  const validateDOI = (doi: string): boolean => {
    const doiPattern = /^10\.\d{4,}\/[^\s]+$/;
    return doiPattern.test(doi.trim());
  };

  const simulateProgress = () => {
    const stages = [
      { stage: 'Validating DOI...', progress: 10 },
      { stage: 'Fetching article metadata...', progress: 25 },
      { stage: 'Running AI analysis...', progress: 50 },
      { stage: 'Checking PDF access...', progress: 75 },
      { stage: 'Finalizing results...', progress: 90 },
    ];

    let currentStage = 0;
    const interval = setInterval(() => {
      if (currentStage < stages.length) {
        setLoading(prev => ({
          ...prev,
          stage: stages[currentStage].stage,
          progress: stages[currentStage].progress
        }));
        currentStage++;
      } else {
        clearInterval(interval);
      }
    }, 800);

    return interval;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!doi.trim()) {
      setError('Please enter a DOI');
      return;
    }

    if (!validateDOI(doi.trim())) {
      setError('Please enter a valid DOI format (e.g., 10.1016/j.kint.2021.02.040)');
      return;
    }

    setError('');
    setResult(null);
    setLoading({
      isLoading: true,
      stage: 'Starting analysis...',
      progress: 5
    });

    const progressInterval = simulateProgress();

    try {
      const response = await axios.get(`http://localhost:4567/summary`, {
        params: { doi: doi.trim() },
        timeout: 60000, // 60 seconds timeout
      });

      clearInterval(progressInterval);
      setLoading({
        isLoading: false,
        stage: 'Complete',
        progress: 100
      });
      
      setResult(response.data);
      setActiveTab('overview');
    } catch (err: any) {
      clearInterval(progressInterval);
      setLoading({
        isLoading: false,
        stage: '',
        progress: 0
      });

      if (err.code === 'ECONNREFUSED') {
        setError('Backend server is not running. Please start the backend server first.');
      } else if (err.response?.status === 404) {
        setError('Article not found. Please check the DOI and try again.');
      } else if (err.response?.status === 400) {
        setError('Invalid DOI format. Please check and try again.');
      } else if (err.code === 'ECONNABORTED') {
        setError('Request timeout. The analysis is taking too long. Please try again.');
      } else {
        setError(err.response?.data?.error || 'An error occurred while analyzing the article. Please try again.');
      }
    }
  };

  const getFieldColor = (field: string): string => {
    const colors: { [key: string]: string } = {
      'Tıp': 'bg-red-100 text-red-800',
      'Fizik': 'bg-blue-100 text-blue-800',
      'Kimya': 'bg-green-100 text-green-800',
      'Bilgisayar': 'bg-purple-100 text-purple-800',
      'Mühendislik': 'bg-orange-100 text-orange-800',
      'Sosyal Bilimler': 'bg-pink-100 text-pink-800',
      'Matematik': 'bg-indigo-100 text-indigo-800',
      'Biyoloji': 'bg-emerald-100 text-emerald-800',
      'Genel': 'bg-gray-100 text-gray-800',
    };
    return colors[field] || colors['Genel'];
  };

  const getDataQualityInfo = (quality: string) => {
    const info = {
      'FULL': { color: 'text-green-600', icon: CheckCircle, text: 'Full Analysis' },
      'PARTIAL': { color: 'text-yellow-600', icon: Clock, text: 'Partial Analysis' },
      'LIMITED': { color: 'text-red-600', icon: AlertCircle, text: 'Limited Analysis' },
    };
    return info[quality as keyof typeof info] || info['LIMITED'];
  };

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Academic Article Analyzer
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          Enter a DOI to get AI-powered analysis of academic articles with detailed insights,
          methodology breakdown, and PDF access information.
        </p>
      </div>

      {/* Search Form */}
      <div className="card max-w-2xl mx-auto mb-8">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="doi" className="form-label">
              DOI (Digital Object Identifier)
            </label>
            <div className="relative">
              <input
                type="text"
                id="doi"
                value={doi}
                onChange={(e) => setDoi(e.target.value)}
                placeholder="e.g., 10.1016/j.kint.2021.02.040"
                className="form-input pr-12"
                disabled={loading.isLoading}
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
            {error && (
              <p className="error-text flex items-center mt-2">
                <AlertCircle className="h-4 w-4 mr-1" />
                {error}
              </p>
            )}
          </div>

          <button
            type="submit"
            disabled={loading.isLoading}
            className="btn-primary w-full flex items-center justify-center space-x-2"
          >
            {loading.isLoading ? (
              <>
                <Loader2 className="h-5 w-5 animate-spin" />
                <span>Analyzing...</span>
              </>
            ) : (
              <>
                <FileText className="h-5 w-5" />
                <span>Analyze Article</span>
              </>
            )}
          </button>
        </form>

        {/* Example DOIs */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <p className="text-sm font-medium text-gray-700 mb-3">Example DOIs:</p>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {[
              { doi: '10.1016/j.kint.2021.02.040', field: 'Medicine' },
              { doi: '10.1038/s41586-021-03819-2', field: 'Physics' },
              { doi: '10.1145/3447548.3467401', field: 'Computer Science' },
              { doi: '10.1007/s44163-022-00022-8', field: 'Social Sciences' },
            ].map((example, index) => (
              <button
                key={index}
                onClick={() => setDoi(example.doi)}
                className="text-left p-2 text-sm bg-gray-50 hover:bg-gray-100 rounded border transition-colors"
                disabled={loading.isLoading}
              >
                <div className="font-mono text-blue-600">{example.doi}</div>
                <div className="text-gray-500">{example.field}</div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Loading Progress */}
      {loading.isLoading && (
        <div className="card max-w-2xl mx-auto mb-8">
          <div className="text-center">
            <div className="mb-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Analyzing Article
            </h3>
            <p className="text-sm text-gray-600 mb-4">{loading.stage}</p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${loading.progress}%` }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 mt-2">{loading.progress}% complete</p>
          </div>
        </div>
      )}

      {/* Results */}
      {result && (
        <AnalysisResults
          result={result}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
        />
      )}
    </div>
  );
}
